"""
核心功能测试
"""

import pytest
import json
from unittest.mock import Mock, patch
from pathlib import Path

from core.models import SpineEvent, EndState, CharacterState, QualityReport
from core.spine import SpineExtractor
from core.alias import AliasManager
from core.phase import PhaseAllocator
from core.drift import DriftDetector
from core.end_state import EndStateManager

class TestSpineEvent:
    """测试SpineEvent模型"""
    
    def test_valid_spine_event(self):
        """测试有效的Spine事件"""
        event = SpineEvent(
            id="1-1",
            type="plot",
            text="罗兰决定保护女巫",
            importance=0.8,
            tension=0.6
        )
        
        assert event.id == "1-1"
        assert event.type == "plot"
        assert event.importance == 0.8
        assert event.tension == 0.6
    
    def test_invalid_importance_score(self):
        """测试无效的重要性分数"""
        with pytest.raises(ValueError):
            SpineEvent(
                id="1-1",
                type="plot", 
                text="测试事件",
                importance=1.5,  # 超出范围
                tension=0.5
            )
    
    def test_text_too_long(self):
        """测试文本过长"""
        with pytest.raises(ValueError):
            SpineEvent(
                id="1-1",
                type="plot",
                text="x" * 101,  # 超过100字符
                importance=0.8,
                tension=0.6
            )

class TestEndState:
    """测试EndState模型"""
    
    def test_valid_end_state(self):
        """测试有效的结束状态"""
        char_states = {
            "罗兰": CharacterState(goal="保护边境", risk="教会威胁", emotion="坚定")
        }
        
        end_state = EndState(
            char_states=char_states,
            open_hooks=["女巫身份暴露"],
            world_delta=["火药技术出现"]
        )
        
        assert len(end_state.char_states) == 1
        assert len(end_state.open_hooks) == 1
        assert end_state.checksum is not None
    
    def test_too_many_hooks(self):
        """测试悬念过多"""
        with pytest.raises(ValueError):
            EndState(
                char_states={},
                open_hooks=["hook1", "hook2", "hook3", "hook4", "hook5", "hook6"],  # 超过5个
                world_delta=[]
            )

class TestAliasManager:
    """测试别名管理器"""
    
    def setup_method(self):
        """设置测试"""
        self.alias_manager = AliasManager()
        # 添加测试数据
        self.alias_manager.add_alias("罗兰", "四王子")
        self.alias_manager.add_alias("罗兰", "殿下")
    
    def test_get_canonical_name(self):
        """测试获取标准名称"""
        assert self.alias_manager.get_canonical_name("四王子") == "罗兰"
        assert self.alias_manager.get_canonical_name("罗兰") == "罗兰"
        assert self.alias_manager.get_canonical_name("未知角色") == "未知角色"
    
    def test_standardize_text(self):
        """测试文本标准化"""
        text = "四王子对殿下说话"
        result = self.alias_manager.standardize_text(text)
        assert "罗兰" in result
        assert "四王子" not in result
    
    def test_extract_character_names(self):
        """测试角色名提取"""
        text = "罗兰和四王子在讨论问题"
        characters = self.alias_manager.extract_character_names(text)
        assert "罗兰" in characters
        assert len(characters) == 1  # 应该只有一个标准名

class TestSpineExtractor:
    """测试Spine抽取器"""
    
    def setup_method(self):
        """设置测试"""
        self.mock_llm = Mock()
        self.spine_extractor = SpineExtractor(self.mock_llm)
    
    def test_calculate_tension_score(self):
        """测试张力分数计算"""
        chapter_summary = {
            "narrative": {"content": "激烈的战斗！危险的情况？"},
            "key_events": [{"event": "战斗"}, {"event": "逃脱"}],
            "key_characters": ["罗兰", "安娜"]
        }
        
        score = self.spine_extractor.calculate_tension_score(chapter_summary)
        assert 0 <= score <= 1
        assert score > 0  # 应该有一定张力
    
    def test_calculate_importance_score(self):
        """测试重要性分数计算"""
        event_text = "罗兰决定保护女巫"
        context = {}
        
        score = self.spine_extractor.calculate_importance_score(event_text, context)
        assert 0 <= score <= 1
        assert score > 0.5  # "决定"是重要关键词

class TestPhaseAllocator:
    """测试Phase分配器"""
    
    def setup_method(self):
        """设置测试"""
        self.phase_allocator = PhaseAllocator()
    
    def test_calculate_phase_weights(self):
        """测试权重计算"""
        group_summaries = {
            "g_001": {
                "group_summary": "开始的故事",
                "main_events": ["初次见面"],
                "token_count": 1000
            },
            "g_002": {
                "group_summary": "激烈的冲突战斗",
                "main_events": ["大战", "决战"],
                "token_count": 1500
            }
        }
        
        weights = self.phase_allocator.calculate_phase_weights(group_summaries)
        
        assert len(weights) == 2
        assert weights["g_002"] > weights["g_001"]  # 冲突章节权重更高
    
    def test_allocate_phases_simple(self):
        """测试简单分配"""
        weights = {"g_001": 0.3, "g_002": 0.5, "g_003": 0.7, "g_004": 0.9}
        
        allocation = self.phase_allocator._allocate_phases_simple(weights)
        
        assert "setup" in allocation
        assert "development" in allocation
        assert "climax" in allocation
        assert "resolution" in allocation
        
        # 检查分配合理性
        all_groups = set()
        for groups in allocation.values():
            all_groups.update(groups)
        
        assert all_groups == set(weights.keys())

class TestDriftDetector:
    """测试漂移检测器"""
    
    def setup_method(self):
        """设置测试"""
        # 使用模拟的编码器
        with patch('core.drift.SentenceTransformer'):
            self.drift_detector = DriftDetector(models=["mock-model"])
    
    def test_extract_script_events(self):
        """测试脚本事件抽取"""
        script_text = """
        罗兰：我决定保护这些女巫。
        旁白：罗兰看到了安娜的能力。
        安娜：谢谢你，殿下。
        """
        
        events = self.drift_detector.extract_script_events(script_text)
        assert len(events) > 0
        assert any("决定" in event for event in events)
    
    def test_get_dynamic_threshold(self):
        """测试动态阈值计算"""
        score_history = [0.3, 0.4, 0.35, 0.45, 0.4]
        
        threshold = self.drift_detector.get_dynamic_threshold(score_history)
        
        assert 0.45 <= threshold <= 0.55
        assert isinstance(threshold, float)

class TestEndStateManager:
    """测试结束状态管理器"""
    
    def setup_method(self):
        """设置测试"""
        self.end_state_manager = EndStateManager()
    
    def test_extract_characters_from_script(self):
        """测试从脚本提取角色"""
        script = """
        罗兰：我要保护边境镇。
        安娜：我会帮助你的。
        旁白：夜莺在暗中观察。
        """
        
        characters = self.end_state_manager._extract_characters_from_script(script)
        
        assert "罗兰" in characters
        assert "安娜" in characters
        assert len(characters) <= 5  # 限制数量
    
    def test_analyze_character_goal(self):
        """测试角色目标分析"""
        script = "罗兰决定保护边境镇的所有居民"
        
        goal = self.end_state_manager._analyze_character_goal("罗兰", script)
        
        assert len(goal) <= 10  # 长度限制
        assert goal != "未明确"  # 应该能识别出目标

if __name__ == "__main__":
    pytest.main([__file__])
