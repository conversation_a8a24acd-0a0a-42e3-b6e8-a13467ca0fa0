"""
GitHub集成机器人
"""

import os
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from github import Github, GithubException
from ..core.models import QualityReport, YellowAlert

logger = logging.getLogger(__name__)

class GitHubBot:
    """GitHub集成机器人"""
    
    def __init__(self, token: Optional[str] = None, repo_name: Optional[str] = None):
        self.token = token or os.getenv("GITHUB_TOKEN")
        self.repo_name = repo_name or os.getenv("GITHUB_REPO")
        
        if not self.token:
            logger.warning("GitHub token not provided, GitHub integration disabled")
            self.github = None
            self.repo = None
            return
        
        try:
            self.github = Github(self.token)
            if self.repo_name:
                self.repo = self.github.get_repo(self.repo_name)
                logger.info(f"Connected to GitHub repo: {self.repo_name}")
            else:
                self.repo = None
                logger.warning("GitHub repo name not provided")
        except GithubException as e:
            logger.error(f"Failed to connect to GitHub: {e}")
            self.github = None
            self.repo = None
    
    def create_yellow_issue(self, quality_report: QualityReport) -> Optional[str]:
        """创建Yellow队列Issue"""
        if not self.repo:
            logger.warning("GitHub repo not available, cannot create issue")
            return None
        
        try:
            title = f"Episode {quality_report.episode_num} Quality Issues - {quality_report.severity}"
            body = self._generate_issue_body(quality_report)
            labels = self._get_issue_labels(quality_report)
            
            issue = self.repo.create_issue(
                title=title,
                body=body,
                labels=labels
            )
            
            logger.info(f"Created GitHub issue #{issue.number} for episode {quality_report.episode_num}")
            return str(issue.number)
            
        except GithubException as e:
            logger.error(f"Failed to create GitHub issue: {e}")
            return None
    
    def _generate_issue_body(self, quality_report: QualityReport) -> str:
        """生成Issue内容"""
        body = f"""## Episode {quality_report.episode_num} Quality Issues

**Divergence Score**: {quality_report.divergence_score:.3f} (threshold: {quality_report.threshold:.3f})
**Status**: {quality_report.status.upper()}
**Severity**: {quality_report.severity}
**Timestamp**: {quality_report.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

### Missing Spine Events
"""
        
        if quality_report.missing_spine_events:
            for event in quality_report.missing_spine_events:
                body += f"- [ ] {event}\n"
        else:
            body += "- No missing spine events detected\n"
        
        body += "\n### Contradicted Events\n"
        
        if quality_report.contradicted_events:
            for event in quality_report.contradicted_events:
                body += f"- [ ] {event}\n"
        else:
            body += "- No contradicted events detected\n"
        
        body += "\n### Suggestions\n"
        
        if quality_report.suggestions:
            for i, suggestion in enumerate(quality_report.suggestions, 1):
                body += f"""
```suggestion
{suggestion}
```
"""
        else:
            body += "- No specific suggestions available\n"
        
        body += f"""
### Metadata
- **Auto-generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Episode**: {quality_report.episode_num}
- **Divergence Score**: {quality_report.divergence_score:.3f}
- **Threshold**: {quality_report.threshold:.3f}

---
*This issue was automatically generated by the novel rewrite quality monitoring system.*
"""
        
        return body
    
    def _get_issue_labels(self, quality_report: QualityReport) -> List[str]:
        """获取Issue标签"""
        labels = ["quality-issue", "auto-generated"]
        
        # 添加严重程度标签
        severity_labels = {
            "LOW": "severity-low",
            "MEDIUM": "severity-medium", 
            "HIGH": "severity-high"
        }
        labels.append(severity_labels.get(quality_report.severity, "severity-unknown"))
        
        # 添加状态标签
        status_labels = {
            "yellow": "status-yellow",
            "red": "status-red",
            "green": "status-green"
        }
        labels.append(status_labels.get(quality_report.status, "status-unknown"))
        
        # 添加集数标签
        labels.append(f"episode-{quality_report.episode_num}")
        
        return labels
    
    def update_issue_with_suggestions(self, issue_number: str, suggestions: List[str]) -> bool:
        """更新Issue添加建议"""
        if not self.repo:
            return False
        
        try:
            issue = self.repo.get_issue(int(issue_number))
            
            comment_body = "### Updated Suggestions\n\n"
            for i, suggestion in enumerate(suggestions, 1):
                comment_body += f"""
```suggestion
{suggestion}
```
"""
            
            comment_body += f"\n*Updated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
            
            issue.create_comment(comment_body)
            logger.info(f"Updated issue #{issue_number} with new suggestions")
            return True
            
        except GithubException as e:
            logger.error(f"Failed to update issue #{issue_number}: {e}")
            return False
    
    def close_issue(self, issue_number: str, resolution_comment: str = "") -> bool:
        """关闭Issue"""
        if not self.repo:
            return False
        
        try:
            issue = self.repo.get_issue(int(issue_number))
            
            if resolution_comment:
                issue.create_comment(f"**Resolution**: {resolution_comment}")
            
            issue.edit(state="closed")
            logger.info(f"Closed issue #{issue_number}")
            return True
            
        except GithubException as e:
            logger.error(f"Failed to close issue #{issue_number}: {e}")
            return False
    
    def get_open_yellow_issues(self) -> List[Dict[str, Any]]:
        """获取开放的Yellow队列Issues"""
        if not self.repo:
            return []
        
        try:
            issues = self.repo.get_issues(
                state="open",
                labels=["quality-issue", "auto-generated"]
            )
            
            yellow_issues = []
            for issue in issues:
                # 解析集数
                episode_num = None
                for label in issue.labels:
                    if label.name.startswith("episode-"):
                        try:
                            episode_num = int(label.name.split("-")[1])
                        except (IndexError, ValueError):
                            pass
                
                yellow_issues.append({
                    "number": issue.number,
                    "title": issue.title,
                    "episode_num": episode_num,
                    "created_at": issue.created_at,
                    "labels": [label.name for label in issue.labels],
                    "url": issue.html_url
                })
            
            return yellow_issues
            
        except GithubException as e:
            logger.error(f"Failed to get yellow issues: {e}")
            return []
    
    def batch_create_issues(self, quality_reports: List[QualityReport]) -> Dict[int, Optional[str]]:
        """批量创建Issues"""
        results = {}
        
        for report in quality_reports:
            if report.status in ["yellow", "red"]:  # 只为有问题的集数创建Issue
                issue_number = self.create_yellow_issue(report)
                results[report.episode_num] = issue_number
        
        logger.info(f"Batch created {len(results)} issues")
        return results
    
    def generate_summary_comment(self, quality_reports: List[QualityReport]) -> str:
        """生成质量总结评论"""
        total_episodes = len(quality_reports)
        yellow_count = sum(1 for r in quality_reports if r.status == "yellow")
        red_count = sum(1 for r in quality_reports if r.status == "red")
        green_count = total_episodes - yellow_count - red_count
        
        avg_divergence = sum(r.divergence_score for r in quality_reports) / total_episodes if total_episodes > 0 else 0
        
        summary = f"""## Quality Summary Report

### Overall Statistics
- **Total Episodes**: {total_episodes}
- **Green (Good)**: {green_count} ({green_count/total_episodes*100:.1f}%)
- **Yellow (Issues)**: {yellow_count} ({yellow_count/total_episodes*100:.1f}%)
- **Red (Critical)**: {red_count} ({red_count/total_episodes*100:.1f}%)
- **Average Divergence Score**: {avg_divergence:.3f}

### Episodes Requiring Attention
"""
        
        problem_episodes = [r for r in quality_reports if r.status in ["yellow", "red"]]
        problem_episodes.sort(key=lambda x: x.divergence_score, reverse=True)
        
        for report in problem_episodes[:10]:  # 显示前10个问题最严重的
            summary += f"- Episode {report.episode_num}: {report.divergence_score:.3f} ({report.status.upper()})\n"
        
        if len(problem_episodes) > 10:
            summary += f"- ... and {len(problem_episodes) - 10} more episodes\n"
        
        summary += f"\n*Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
        
        return summary
    
    def post_summary_comment(self, issue_number: str, quality_reports: List[QualityReport]) -> bool:
        """在指定Issue中发布总结评论"""
        if not self.repo:
            return False
        
        try:
            issue = self.repo.get_issue(int(issue_number))
            summary = self.generate_summary_comment(quality_reports)
            issue.create_comment(summary)
            
            logger.info(f"Posted summary comment to issue #{issue_number}")
            return True
            
        except GithubException as e:
            logger.error(f"Failed to post summary comment: {e}")
            return False
