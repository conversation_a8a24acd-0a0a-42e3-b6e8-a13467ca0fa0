#!/usr/bin/env python3
"""
完整的小说改编流水线
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.models import ProcessingConfig
from pipelines.build_spine import SpinePipeline
from pipelines.build_phases import PhasePipeline
from pipelines.build_episodes import EpisodePipeline
from utils.quality_monitor import QualityMonitor
from io.github_bot import GitHubBot
from io.load_save import load_json, save_json
from config.settings import settings, create_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('novel_rewrite.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FullPipeline:
    """完整的小说改编流水线"""
    
    def __init__(self, llm_client, config: ProcessingConfig):
        self.llm_client = llm_client
        self.config = config
        self.output_dir = Path(settings.OUTPUT_DIR)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化组件
        self.spine_pipeline = SpinePipeline(llm_client, str(self.output_dir))
        self.phase_pipeline = PhasePipeline(llm_client, str(self.output_dir))
        self.episode_pipeline = EpisodePipeline(llm_client, str(self.output_dir))
        
        # 监控组件
        if config.enable_monitoring:
            self.quality_monitor = QualityMonitor(str(self.output_dir))
        else:
            self.quality_monitor = None
        
        # GitHub集成
        if config.github_integration:
            self.github_bot = GitHubBot()
        else:
            self.github_bot = None
    
    def run(self, input_file: str) -> Dict[str, Any]:
        """运行完整流水线"""
        logger.info("Starting full novel rewrite pipeline")
        
        try:
            # 1. 加载输入数据
            logger.info("Loading input data...")
            input_data = load_json(input_file)
            chapter_summaries = input_data.get("chapters", [])
            
            if not chapter_summaries:
                raise ValueError("No chapters found in input data")
            
            logger.info(f"Loaded {len(chapter_summaries)} chapters")
            
            # 2. Spine事件抽取
            logger.info("Phase 1: Extracting spine events...")
            spine_results = self.spine_pipeline.process_chapters(chapter_summaries)
            
            # 验证Spine质量
            spine_validation = self.spine_pipeline.validate_spine_events(spine_results, chapter_summaries)
            if spine_validation["overall_quality"] == "poor":
                logger.warning("Spine extraction quality is poor, consider manual review")
            
            # 3. Phase分配
            logger.info("Phase 2: Allocating story phases...")
            # 这里需要先从spine_results构建group_summaries
            group_summaries = self._build_group_summaries(spine_results, chapter_summaries)
            phase_results = self.phase_pipeline.allocate_phases(group_summaries)
            
            # 4. 剧集生成
            logger.info("Phase 3: Generating episodes...")
            episode_results = self.episode_pipeline.generate_episodes(
                spine_results=spine_results,
                phase_allocation=phase_results["allocation"],
                chapter_summaries=chapter_summaries,
                compression_ratio=self.config.compression_ratio,
                max_episodes=self.config.max_episodes
            )
            
            # 5. 质量监控
            if self.quality_monitor:
                logger.info("Phase 4: Quality monitoring...")
                self._run_quality_monitoring(episode_results)
            
            # 6. GitHub集成
            if self.github_bot:
                logger.info("Phase 5: GitHub integration...")
                self._run_github_integration(episode_results)
            
            # 7. 生成最终报告
            final_results = self._generate_final_report(
                spine_results, phase_results, episode_results
            )
            
            logger.info("Pipeline completed successfully")
            return final_results
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            raise
    
    def _build_group_summaries(self, spine_results: Dict[str, Any], 
                              chapter_summaries: list) -> Dict[str, Any]:
        """从spine结果构建组摘要（简化版本）"""
        # 这里简化处理，实际应该有更复杂的分组逻辑
        group_summaries = {}
        
        for chapter_id, spine_data in spine_results.items():
            chapter_num = spine_data["chapter_number"]
            
            # 找到对应的章节摘要
            chapter_summary = None
            for summary in chapter_summaries:
                if summary.get('basic_info', {}).get('chapter_number') == chapter_num:
                    chapter_summary = summary
                    break
            
            if chapter_summary:
                # 计算张力分数
                tension_score = self._calculate_chapter_tension(spine_data["spine_events"])
                
                # 估算token数
                content = chapter_summary.get('narrative', {}).get('content', '')
                token_count = len(content) * 0.7  # 粗略估算
                
                group_summaries[f"g_{chapter_num:03d}"] = {
                    "chapters": [chapter_num],
                    "group_summary": content[:200] + "..." if len(content) > 200 else content,
                    "main_events": [event["text"] for event in spine_data["spine_events"]],
                    "tension_score": tension_score,
                    "token_count": token_count
                }
        
        return group_summaries
    
    def _calculate_chapter_tension(self, spine_events: list) -> float:
        """计算章节张力分数"""
        if not spine_events:
            return 0.0
        
        tension_scores = [event.get("tension", 0.0) for event in spine_events]
        return sum(tension_scores) / len(tension_scores)
    
    def _run_quality_monitoring(self, episode_results: Dict[str, Any]) -> None:
        """运行质量监控"""
        # 这里应该从episode_results中提取质量报告
        # 简化版本，实际需要更复杂的质量分析
        logger.info("Quality monitoring completed (placeholder)")
    
    def _run_github_integration(self, episode_results: Dict[str, Any]) -> None:
        """运行GitHub集成"""
        if not self.github_bot.repo:
            logger.warning("GitHub repo not configured, skipping integration")
            return
        
        logger.info("GitHub integration completed (placeholder)")
    
    def _generate_final_report(self, spine_results: Dict[str, Any], 
                              phase_results: Dict[str, Any],
                              episode_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终报告"""
        report = {
            "pipeline_summary": {
                "total_chapters": len(spine_results),
                "total_spine_events": sum(data["event_count"] for data in spine_results.values()),
                "total_episodes": len(episode_results.get("episodes", [])),
                "processing_config": self.config.dict()
            },
            "spine_results": spine_results,
            "phase_results": phase_results,
            "episode_results": episode_results,
            "output_files": {
                "spine_events": str(self.output_dir / "spine_events.json"),
                "phase_allocation": str(self.output_dir / "phase_allocation.json"),
                "episodes": str(self.output_dir / "episodes.json")
            }
        }
        
        # 保存最终报告
        report_file = self.output_dir / "final_report.json"
        save_json(report, str(report_file))
        
        logger.info(f"Final report saved to {report_file}")
        return report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Novel Rewrite Pipeline")
    parser.add_argument("input_file", help="Input JSON file with chapter summaries")
    parser.add_argument("--output_dir", default="./output", help="Output directory")
    parser.add_argument("--compression", type=float, default=0.3, help="Compression ratio")
    parser.add_argument("--max_episodes", type=int, help="Maximum number of episodes")
    parser.add_argument("--enable_monitoring", action="store_true", help="Enable quality monitoring")
    parser.add_argument("--github_integration", action="store_true", help="Enable GitHub integration")
    
    args = parser.parse_args()
    
    # 更新设置
    settings.OUTPUT_DIR = Path(args.output_dir)
    
    # 创建配置
    config = ProcessingConfig(
        compression_ratio=args.compression,
        max_episodes=args.max_episodes,
        enable_monitoring=args.enable_monitoring,
        github_integration=args.github_integration
    )
    
    # 创建LLM客户端（这里需要实际的LLM客户端实现）
    class MockLLMClient:
        def call_json_response(self, prompt, expected_fields=None):
            # 模拟LLM响应
            return {"spine_events": []}
    
    llm_client = MockLLMClient()
    
    # 运行流水线
    try:
        pipeline = FullPipeline(llm_client, config)
        results = pipeline.run(args.input_file)
        
        print(f"Pipeline completed successfully!")
        print(f"Results saved to: {settings.OUTPUT_DIR}")
        print(f"Total episodes generated: {results['pipeline_summary']['total_episodes']}")
        
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
