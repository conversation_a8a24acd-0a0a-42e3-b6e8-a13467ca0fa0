"""
剧集生成流水线
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from ..core.drift import DriftDetector
from ..core.end_state import EndStateManager
from ..core.models import QualityReport
from ..utils.quality_monitor import QualityMonitor
from ..io.load_save import load_json, save_json

logger = logging.getLogger(__name__)

class EpisodePipeline:
    """剧集生成流水线"""
    
    def __init__(self, llm_client, output_dir: str = "./output"):
        self.llm_client = llm_client
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化组件
        self.drift_detector = DriftDetector()
        self.end_state_manager = EndStateManager(str(self.output_dir))
        self.quality_monitor = QualityMonitor(str(self.output_dir))
        
        # 统计信息
        self.stats = {
            "total_episodes": 0,
            "generated_episodes": 0,
            "yellow_episodes": 0,
            "red_episodes": 0,
            "avg_divergence_score": 0.0
        }
    
    def generate_episodes(self, 
                         spine_results: Dict[str, Any],
                         phase_allocation: Dict[str, Any],
                         chapter_summaries: List[Dict[str, Any]],
                         compression_ratio: float = 0.3,
                         max_episodes: Optional[int] = None) -> Dict[str, Any]:
        """生成剧集"""
        logger.info("Starting episode generation")
        
        # 构建剧集分配（简化版本）
        episode_allocation = self._build_episode_allocation(
            spine_results, phase_allocation, max_episodes
        )
        
        self.stats["total_episodes"] = len(episode_allocation)
        
        episodes = []
        quality_reports = []
        previous_end_state = None
        
        for i, episode_info in enumerate(episode_allocation, 1):
            try:
                logger.info(f"Generating episode {i}/{len(episode_allocation)}")
                
                # 生成剧集结构
                episode_structure = self._generate_episode_structure(
                    episode_info, spine_results, previous_end_state, compression_ratio
                )
                
                # 生成剧本
                episode_script = self._generate_episode_script(
                    episode_structure, compression_ratio
                )
                
                # 质量检测
                quality_report = self._check_episode_quality(
                    episode_script, episode_info, i
                )
                
                # 生成结束状态
                end_state = self.end_state_manager.generate_end_state(
                    episode_script, i, previous_end_state
                )
                
                # 保存剧集
                episode_data = {
                    "episode_number": i,
                    "structure": episode_structure,
                    "script": episode_script,
                    "end_state": end_state.dict(),
                    "quality_report": quality_report.dict()
                }
                
                episodes.append(episode_data)
                quality_reports.append(quality_report)
                
                # 更新统计
                self._update_episode_stats(quality_report)
                
                # 添加到质量监控
                self.quality_monitor.add_quality_report(quality_report)
                
                previous_end_state = end_state
                self.stats["generated_episodes"] += 1
                
                logger.info(f"Episode {i} completed with status: {quality_report.status}")
                
            except Exception as e:
                logger.error(f"Failed to generate episode {i}: {e}")
                continue
        
        # 保存结果
        results = {
            "episodes": episodes,
            "quality_summary": self._generate_quality_summary(quality_reports),
            "statistics": self.stats
        }
        
        self._save_episode_results(results)
        
        logger.info(f"Episode generation completed. Generated {self.stats['generated_episodes']}/{self.stats['total_episodes']} episodes")
        
        return results
    
    def _build_episode_allocation(self, spine_results: Dict[str, Any], 
                                 phase_allocation: Dict[str, Any],
                                 max_episodes: Optional[int]) -> List[Dict[str, Any]]:
        """构建剧集分配（简化版本）"""
        # 这里是简化的实现，实际应该更复杂
        episodes = []
        
        # 按章节顺序分配到剧集
        chapters = sorted(spine_results.keys(), key=int)
        
        if max_episodes:
            chapters_per_episode = max(1, len(chapters) // max_episodes)
        else:
            chapters_per_episode = 2  # 默认每集2章
        
        for i in range(0, len(chapters), chapters_per_episode):
            episode_chapters = chapters[i:i + chapters_per_episode]
            
            # 收集该集的spine事件
            episode_spine_events = []
            for chapter_id in episode_chapters:
                chapter_data = spine_results[chapter_id]
                episode_spine_events.extend(chapter_data["spine_events"])
            
            episodes.append({
                "episode_number": len(episodes) + 1,
                "chapters": [int(ch) for ch in episode_chapters],
                "spine_events": episode_spine_events
            })
        
        return episodes
    
    def _generate_episode_structure(self, episode_info: Dict[str, Any],
                                   spine_results: Dict[str, Any],
                                   previous_end_state: Optional[Any],
                                   compression_ratio: float) -> Dict[str, Any]:
        """生成剧集结构"""
        # 模拟结构生成
        structure = {
            "episode_number": episode_info["episode_number"],
            "total_scenes": 5,
            "estimated_duration": 12.0,
            "spine_coverage": [event["id"] for event in episode_info["spine_events"]],
            "scenes": []
        }
        
        # 生成场景
        for i in range(5):
            scene = {
                "scene_number": i + 1,
                "title": f"场景{i + 1}",
                "location": "边境镇",
                "time": "白天",
                "characters": ["罗兰"],
                "main_conflict": "推进剧情",
                "emotional_arc": "平稳发展",
                "key_events": [],
                "spine_events_covered": [],
                "transition": "淡出",
                "estimated_duration": 2.4
            }
            structure["scenes"].append(scene)
        
        return structure
    
    def _generate_episode_script(self, episode_structure: Dict[str, Any],
                                compression_ratio: float) -> str:
        """生成剧集剧本"""
        # 模拟剧本生成
        script_lines = [
            f"# 第{episode_structure['episode_number']}集剧本\n",
            "## 场景1：边境镇 - 白天\n",
            "罗兰：我必须保护这个镇子。\n",
            "旁白：罗兰看着远方，思考着未来的挑战。\n",
            "安娜：殿下，我会帮助您的。\n",
            "罗兰：谢谢你，安娜。我们一起努力。\n",
            "\n## 场景2：城堡大厅 - 傍晚\n",
            "卡特：殿下，边境的情况如何？\n",
            "罗兰：我们需要更多的准备。\n",
            "旁白：夜幕降临，新的挑战即将到来。\n"
        ]
        
        return "".join(script_lines)
    
    def _check_episode_quality(self, episode_script: str, 
                              episode_info: Dict[str, Any],
                              episode_number: int) -> QualityReport:
        """检查剧集质量"""
        # 提取spine事件文本
        spine_events = [event["text"] for event in episode_info["spine_events"]]
        
        # 使用漂移检测器
        quality_report = self.drift_detector.detect_drift(
            episode_script, spine_events, episode_number
        )
        
        return quality_report
    
    def _update_episode_stats(self, quality_report: QualityReport) -> None:
        """更新剧集统计"""
        if quality_report.status == "yellow":
            self.stats["yellow_episodes"] += 1
        elif quality_report.status == "red":
            self.stats["red_episodes"] += 1
        
        # 更新平均漂移分数
        total_episodes = self.stats["generated_episodes"] + 1
        current_avg = self.stats["avg_divergence_score"]
        new_avg = (current_avg * self.stats["generated_episodes"] + quality_report.divergence_score) / total_episodes
        self.stats["avg_divergence_score"] = new_avg
    
    def _generate_quality_summary(self, quality_reports: List[QualityReport]) -> Dict[str, Any]:
        """生成质量摘要"""
        if not quality_reports:
            return {}
        
        total = len(quality_reports)
        green_count = sum(1 for r in quality_reports if r.status == "green")
        yellow_count = sum(1 for r in quality_reports if r.status == "yellow")
        red_count = sum(1 for r in quality_reports if r.status == "red")
        
        avg_divergence = sum(r.divergence_score for r in quality_reports) / total
        
        return {
            "total_episodes": total,
            "status_distribution": {
                "green": {"count": green_count, "percentage": green_count / total * 100},
                "yellow": {"count": yellow_count, "percentage": yellow_count / total * 100},
                "red": {"count": red_count, "percentage": red_count / total * 100}
            },
            "avg_divergence_score": avg_divergence,
            "quality_trend": "stable"  # 简化版本
        }
    
    def _save_episode_results(self, results: Dict[str, Any]) -> None:
        """保存剧集结果"""
        # 保存完整结果
        results_file = self.output_dir / "episodes.json"
        save_json(results, str(results_file))
        
        # 保存单独的剧本文件
        scripts_dir = self.output_dir / "scripts"
        scripts_dir.mkdir(exist_ok=True)
        
        for episode in results["episodes"]:
            episode_num = episode["episode_number"]
            script_file = scripts_dir / f"episode_{episode_num:02d}.txt"
            
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(episode["script"])
        
        logger.info(f"Episode results saved to {results_file}")
        logger.info(f"Individual scripts saved to {scripts_dir}")
    
    def load_episodes(self, file_path: Optional[str] = None) -> Dict[str, Any]:
        """加载已生成的剧集"""
        if file_path is None:
            file_path = str(self.output_dir / "episodes.json")
        
        try:
            return load_json(file_path)
        except FileNotFoundError:
            logger.error(f"Episodes file not found: {file_path}")
            raise
