"""
Spine构建流水线
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from tqdm import tqdm

from ..core.spine import SpineExtractor
from ..core.alias import AliasManager
from ..io.load_save import load_json, save_json
from ..config.settings import settings

logger = logging.getLogger(__name__)

class SpinePipeline:
    """Spine事件抽取流水线"""
    
    def __init__(self, llm_client, output_dir: str = "./output"):
        self.llm_client = llm_client
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化组件
        self.alias_manager = AliasManager()
        self.spine_extractor = SpineExtractor(llm_client, settings.get_alias_map_path())
        
        # 统计信息
        self.stats = {
            "total_chapters": 0,
            "processed_chapters": 0,
            "total_spine_events": 0,
            "failed_chapters": []
        }
    
    def process_chapters(self, chapter_summaries: List[Dict[str, Any]], 
                        auto_discover_aliases: bool = True) -> Dict[str, Any]:
        """处理章节摘要，抽取Spine事件"""
        logger.info(f"Starting spine extraction for {len(chapter_summaries)} chapters")
        
        self.stats["total_chapters"] = len(chapter_summaries)
        
        # 自动发现别名（可选）
        if auto_discover_aliases:
            self._auto_discover_aliases(chapter_summaries)
        
        # 处理每个章节
        spine_results = {}
        
        for chapter_summary in tqdm(chapter_summaries, desc="Extracting spine events"):
            try:
                chapter_num = chapter_summary.get('basic_info', {}).get('chapter_number')
                if chapter_num is None:
                    logger.warning("Chapter number not found, skipping")
                    continue
                
                # 抽取Spine事件
                spine_events = self.spine_extractor.extract_spine_events(chapter_summary)
                
                # 保存结果
                spine_results[str(chapter_num)] = {
                    "chapter_number": chapter_num,
                    "spine_events": [event.dict() for event in spine_events],
                    "event_count": len(spine_events)
                }
                
                self.stats["processed_chapters"] += 1
                self.stats["total_spine_events"] += len(spine_events)
                
                logger.debug(f"Chapter {chapter_num}: extracted {len(spine_events)} spine events")
                
            except Exception as e:
                logger.error(f"Failed to process chapter {chapter_num}: {e}")
                self.stats["failed_chapters"].append(chapter_num)
                continue
        
        # 保存结果
        output_file = self.output_dir / "spine_events.json"
        save_json(spine_results, str(output_file))
        
        # 保存统计信息
        self._save_statistics()
        
        logger.info(f"Spine extraction completed. Processed {self.stats['processed_chapters']}/{self.stats['total_chapters']} chapters")
        logger.info(f"Total spine events extracted: {self.stats['total_spine_events']}")
        
        return spine_results
    
    def _auto_discover_aliases(self, chapter_summaries: List[Dict[str, Any]]) -> None:
        """自动发现角色别名"""
        logger.info("Auto-discovering character aliases...")
        
        # 收集所有文本
        texts = []
        for summary in chapter_summaries:
            if 'narrative' in summary and 'content' in summary['narrative']:
                texts.append(summary['narrative']['content'])
        
        # 发现别名
        discovered_aliases = self.alias_manager.auto_discover_aliases(texts)
        
        if discovered_aliases:
            logger.info(f"Discovered {len(discovered_aliases)} potential alias groups")
            
            # 更新别名映射
            self.alias_manager.update_from_discovery(discovered_aliases)
            self.alias_manager.save_alias_map()
            
            # 重新加载到spine_extractor
            self.spine_extractor.alias_map = self.alias_manager.alias_map
        else:
            logger.info("No new aliases discovered")
    
    def validate_spine_events(self, spine_results: Dict[str, Any], 
                            chapter_summaries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证抽取的Spine事件质量"""
        logger.info("Validating spine events...")
        
        validation_results = {
            "overall_quality": "good",
            "chapter_validations": {},
            "issues": [],
            "statistics": {
                "avg_events_per_chapter": 0,
                "importance_distribution": {},
                "tension_distribution": {}
            }
        }
        
        total_events = 0
        importance_scores = []
        tension_scores = []
        
        # 创建章节摘要映射
        chapter_map = {
            summary.get('basic_info', {}).get('chapter_number'): summary 
            for summary in chapter_summaries
        }
        
        for chapter_id, spine_data in spine_results.items():
            chapter_num = spine_data["chapter_number"]
            spine_events = spine_data["spine_events"]
            
            # 基本统计
            total_events += len(spine_events)
            
            for event in spine_events:
                importance_scores.append(event["importance"])
                tension_scores.append(event["tension"])
            
            # 章节级验证
            chapter_summary = chapter_map.get(chapter_num)
            if chapter_summary:
                chapter_validation = self._validate_chapter_spine(spine_events, chapter_summary)
                validation_results["chapter_validations"][chapter_id] = chapter_validation
                
                if chapter_validation["issues"]:
                    validation_results["issues"].extend([
                        f"Chapter {chapter_num}: {issue}" for issue in chapter_validation["issues"]
                    ])
        
        # 计算统计信息
        if spine_results:
            validation_results["statistics"]["avg_events_per_chapter"] = total_events / len(spine_results)
        
        if importance_scores:
            validation_results["statistics"]["importance_distribution"] = {
                "mean": sum(importance_scores) / len(importance_scores),
                "min": min(importance_scores),
                "max": max(importance_scores)
            }
        
        if tension_scores:
            validation_results["statistics"]["tension_distribution"] = {
                "mean": sum(tension_scores) / len(tension_scores),
                "min": min(tension_scores),
                "max": max(tension_scores)
            }
        
        # 确定整体质量
        issue_count = len(validation_results["issues"])
        if issue_count == 0:
            validation_results["overall_quality"] = "excellent"
        elif issue_count <= len(spine_results) * 0.1:  # 10%以下有问题
            validation_results["overall_quality"] = "good"
        elif issue_count <= len(spine_results) * 0.3:  # 30%以下有问题
            validation_results["overall_quality"] = "fair"
        else:
            validation_results["overall_quality"] = "poor"
        
        # 保存验证结果
        validation_file = self.output_dir / "spine_validation.json"
        save_json(validation_results, str(validation_file))
        
        logger.info(f"Spine validation completed. Overall quality: {validation_results['overall_quality']}")
        
        return validation_results
    
    def _validate_chapter_spine(self, spine_events: List[Dict[str, Any]], 
                               chapter_summary: Dict[str, Any]) -> Dict[str, Any]:
        """验证单个章节的Spine事件"""
        issues = []
        
        # 检查事件数量
        if len(spine_events) == 0:
            issues.append("No spine events extracted")
        elif len(spine_events) > 4:
            issues.append(f"Too many spine events: {len(spine_events)}")
        
        # 检查事件质量
        for i, event in enumerate(spine_events):
            # 检查必要字段
            required_fields = ["id", "type", "text", "importance", "tension"]
            for field in required_fields:
                if field not in event:
                    issues.append(f"Event {i+1}: missing field '{field}'")
            
            # 检查分数范围
            if "importance" in event and not (0 <= event["importance"] <= 1):
                issues.append(f"Event {i+1}: importance score out of range")
            
            if "tension" in event and not (0 <= event["tension"] <= 1):
                issues.append(f"Event {i+1}: tension score out of range")
            
            # 检查文本长度
            if "text" in event and len(event["text"]) > 100:
                issues.append(f"Event {i+1}: text too long ({len(event['text'])} chars)")
        
        return {
            "issues": issues,
            "event_count": len(spine_events),
            "quality": "good" if not issues else "poor"
        }
    
    def _save_statistics(self) -> None:
        """保存统计信息"""
        stats_file = self.output_dir / "spine_statistics.json"
        
        # 添加别名统计
        alias_stats = self.alias_manager.get_statistics()
        self.stats.update({"alias_statistics": alias_stats})
        
        save_json(self.stats, str(stats_file))
        logger.info(f"Statistics saved to {stats_file}")
    
    def load_spine_events(self, file_path: Optional[str] = None) -> Dict[str, Any]:
        """加载已保存的Spine事件"""
        if file_path is None:
            file_path = str(self.output_dir / "spine_events.json")
        
        try:
            return load_json(file_path)
        except FileNotFoundError:
            logger.error(f"Spine events file not found: {file_path}")
            raise
    
    def export_spine_summary(self, spine_results: Dict[str, Any]) -> str:
        """导出Spine事件摘要"""
        summary_lines = ["# Spine Events Summary\n"]
        
        total_events = sum(data["event_count"] for data in spine_results.values())
        summary_lines.append(f"**Total Chapters**: {len(spine_results)}")
        summary_lines.append(f"**Total Spine Events**: {total_events}")
        summary_lines.append(f"**Average Events per Chapter**: {total_events/len(spine_results):.1f}\n")
        
        # 按章节列出事件
        for chapter_id in sorted(spine_results.keys(), key=int):
            data = spine_results[chapter_id]
            chapter_num = data["chapter_number"]
            events = data["spine_events"]
            
            summary_lines.append(f"## Chapter {chapter_num}")
            
            for event in events:
                summary_lines.append(f"- **{event['id']}** ({event['type']}): {event['text']}")
                summary_lines.append(f"  - Importance: {event['importance']:.2f}, Tension: {event['tension']:.2f}")
            
            summary_lines.append("")
        
        summary_content = "\n".join(summary_lines)
        
        # 保存摘要
        summary_file = self.output_dir / "spine_summary.md"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        logger.info(f"Spine summary exported to {summary_file}")
        return str(summary_file)
