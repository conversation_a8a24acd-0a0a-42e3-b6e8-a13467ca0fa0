"""
Phase分配流水线
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from ..core.phase import PhaseAllocator
from ..core.models import PhaseAllocation
from ..io.load_save import load_json, save_json

logger = logging.getLogger(__name__)

class PhasePipeline:
    """Phase分配流水线"""
    
    def __init__(self, llm_client, output_dir: str = "./output"):
        self.llm_client = llm_client
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化组件
        self.phase_allocator = PhaseAllocator()
        
        # 统计信息
        self.stats = {
            "total_groups": 0,
            "phase_distribution": {},
            "adjustment_rounds": 0
        }
    
    def allocate_phases(self, group_summaries: Dict[str, Any]) -> Dict[str, Any]:
        """分配故事阶段"""
        logger.info(f"Starting phase allocation for {len(group_summaries)} groups")
        
        self.stats["total_groups"] = len(group_summaries)
        
        try:
            # 执行动态分配
            allocation_result = self.phase_allocator.allocate_phases_dynamic(
                group_summaries=group_summaries
            )
            
            # 提取结果
            allocation = allocation_result["allocation"]
            debug_report = allocation_result["debug_report"]
            validation_issues = allocation_result["validation_issues"]
            weights = allocation_result["weights"]
            
            # 更新统计信息
            self._update_statistics(allocation, debug_report)
            
            # 保存结果
            self._save_results(allocation_result)
            
            # 记录结果
            logger.info("Phase allocation completed")
            for phase, groups in allocation.dict().items():
                logger.info(f"  {phase}: {len(groups)} groups")
            
            if validation_issues:
                logger.warning(f"Validation issues found: {validation_issues}")
            
            return allocation_result
            
        except Exception as e:
            logger.error(f"Phase allocation failed: {e}")
            raise
    
    def _update_statistics(self, allocation: PhaseAllocation, debug_report: Dict[str, Any]) -> None:
        """更新统计信息"""
        # 阶段分布
        for phase, groups in allocation.dict().items():
            self.stats["phase_distribution"][phase] = len(groups)
        
        # 调整轮数（从debug报告中获取，如果有的话）
        self.stats["adjustment_rounds"] = debug_report.get("adjustment_rounds", 0)
    
    def _save_results(self, allocation_result: Dict[str, Any]) -> None:
        """保存分配结果"""
        # 保存主要分配结果
        allocation_file = self.output_dir / "phase_allocation.json"
        save_json({
            "allocation": allocation_result["allocation"].dict(),
            "validation_issues": allocation_result["validation_issues"],
            "weights": allocation_result["weights"]
        }, str(allocation_file))
        
        # 保存调试报告
        debug_file = self.output_dir / "phase_allocation_debug.json"
        save_json(allocation_result["debug_report"], str(debug_file))
        
        # 保存统计信息
        stats_file = self.output_dir / "phase_statistics.json"
        save_json(self.stats, str(stats_file))
        
        logger.info(f"Phase allocation results saved to {allocation_file}")
    
    def load_phase_allocation(self, file_path: Optional[str] = None) -> Dict[str, Any]:
        """加载已保存的Phase分配"""
        if file_path is None:
            file_path = str(self.output_dir / "phase_allocation.json")
        
        try:
            return load_json(file_path)
        except FileNotFoundError:
            logger.error(f"Phase allocation file not found: {file_path}")
            raise
    
    def validate_allocation(self, allocation: PhaseAllocation, 
                          group_summaries: Dict[str, Any]) -> List[str]:
        """验证分配结果"""
        return self.phase_allocator.validate_phase_allocation(
            allocation.dict(), group_summaries
        )
    
    def export_allocation_summary(self, allocation_result: Dict[str, Any]) -> str:
        """导出分配摘要"""
        allocation = allocation_result["allocation"]
        debug_report = allocation_result["debug_report"]
        
        summary_lines = ["# Phase Allocation Summary\n"]
        
        # 基本信息
        total_groups = sum(len(groups) for groups in allocation.dict().values())
        summary_lines.append(f"**Total Groups**: {total_groups}")
        summary_lines.append(f"**Validation Issues**: {len(allocation_result['validation_issues'])}\n")
        
        # 阶段分布
        summary_lines.append("## Phase Distribution\n")
        for phase, groups in allocation.dict().items():
            ratio = len(groups) / total_groups if total_groups > 0 else 0
            target_ratio = self.phase_allocator.target_ratios.get(phase, 0)
            
            summary_lines.append(f"### {phase.title()}")
            summary_lines.append(f"- Groups: {len(groups)} ({ratio:.1%})")
            summary_lines.append(f"- Target: {target_ratio:.1%}")
            summary_lines.append(f"- Groups: {', '.join(groups)}\n")
        
        # 权重分析
        if "weight_distribution" in debug_report:
            summary_lines.append("## Weight Analysis\n")
            weights = debug_report["weight_distribution"]
            
            summary_lines.append(f"- Average Weight: {sum(weights.values())/len(weights):.3f}")
            summary_lines.append(f"- Min Weight: {min(weights.values()):.3f}")
            summary_lines.append(f"- Max Weight: {max(weights.values()):.3f}\n")
        
        # 验证问题
        if allocation_result["validation_issues"]:
            summary_lines.append("## Validation Issues\n")
            for issue in allocation_result["validation_issues"]:
                summary_lines.append(f"- {issue}")
            summary_lines.append("")
        
        summary_content = "\n".join(summary_lines)
        
        # 保存摘要
        summary_file = self.output_dir / "phase_allocation_summary.md"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        logger.info(f"Phase allocation summary exported to {summary_file}")
        return str(summary_file)
