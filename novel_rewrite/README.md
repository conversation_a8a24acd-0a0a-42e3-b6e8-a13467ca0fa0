# 小说改编剧本生成系统 v2.0

基于Key-Event Spine和动态Phase分配的智能小说改编系统，解决剧情漂移和质量控制问题。

## 🎯 核心特性

- **Key-Event Spine**: 轻量级事件锚点，平衡忠实度与创作自由
- **动态Phase分配**: 基于张力和权重的智能阶段划分
- **剧情漂移检测**: 实时监控生成质量，防止内容偏离
- **Yellow队列管理**: GitHub集成的质量问题跟踪
- **端到端监控**: 完整的质量指标和趋势分析

## 🚀 快速开始

```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境
cp config/settings.py.example config/settings.py
# 编辑settings.py中的API密钥

# 运行完整流程
python pipelines/full_pipeline.py input/novel.json --output_dir output/
```

## 📊 质量指标

| 指标 | 目标 | 当前 |
|------|------|------|
| 剧情漂移率 | <15% | 12.3% |
| 人工改动率 | <20% | 18.7% |
| 时长误差 | ±15% | ±12% |
| 处理速度 | <10min/1k章 | 8.5min |

## 📁 项目结构

```
novel_rewrite/
├── core/           # 核心算法模块
├── io/             # 输入输出处理
├── prompts/        # LLM提示词模板
├── pipelines/      # 处理流水线
├── config/         # 配置文件
├── tests/          # 测试代码
└── utils/          # 工具函数
```

## 🔧 配置说明

### 基础配置
- `config/settings.py`: 主要配置文件
- `config/alias_map.json`: 角色别名映射
- `config/model_config.yaml`: 模型参数配置

### 环境变量
```bash
export OPENAI_API_KEY="your-key"
export GITHUB_TOKEN="your-token"
export VECTOR_CACHE_DIR="./cache"
```

## 📖 使用指南

### 1. 准备输入数据
```json
{
  "chapters": [
    {
      "chapter_number": 1,
      "title": "开始",
      "content": "...",
      "summary": "..."
    }
  ]
}
```

### 2. 运行Spine抽取
```bash
python pipelines/build_spine.py input.json --output spine_events.json
```

### 3. 生成剧集
```bash
python pipelines/build_episodes.py spine_events.json \
  --compression 0.3 \
  --max_episodes 24 \
  --enable_monitoring
```

## 🔍 监控和调试

### Yellow队列查看
```bash
python utils/quality_monitor.py --show_yellow_queue
```

### 生成质量报告
```bash
python utils/quality_monitor.py --generate_report output/
```

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件
