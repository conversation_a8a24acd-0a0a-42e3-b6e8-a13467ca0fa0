"""
质量监控工具
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path
import matplotlib.pyplot as plt
import pandas as pd

from ..core.models import QualityReport, YellowAlert
from ..io.load_save import save_json, load_json

logger = logging.getLogger(__name__)

class QualityMonitor:
    """质量监控器"""
    
    def __init__(self, output_dir: str = "./output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.reports_dir = self.output_dir / "quality_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        self.yellow_queue_dir = self.output_dir / "yellow_queue"
        self.yellow_queue_dir.mkdir(exist_ok=True)
        
        self.metrics_file = self.output_dir / "quality_metrics.json"
        
    def add_quality_report(self, report: QualityReport) -> None:
        """添加质量报告"""
        # 保存单个报告
        report_file = self.reports_dir / f"episode_{report.episode_num:02d}.json"
        save_json(report.dict(), str(report_file))
        
        # 更新总体指标
        self._update_metrics(report)
        
        # 如果是Yellow或Red状态，添加到Yellow队列
        if report.status in ["yellow", "red"]:
            self._add_to_yellow_queue(report)
        
        logger.info(f"Added quality report for episode {report.episode_num}: {report.status}")
    
    def _update_metrics(self, report: QualityReport) -> None:
        """更新总体指标"""
        try:
            # 加载现有指标
            if self.metrics_file.exists():
                metrics = load_json(str(self.metrics_file))
            else:
                metrics = {
                    "total_episodes": 0,
                    "status_counts": {"green": 0, "yellow": 0, "red": 0},
                    "avg_divergence": 0.0,
                    "trend_data": [],
                    "last_updated": None
                }
            
            # 更新计数
            metrics["total_episodes"] += 1
            metrics["status_counts"][report.status] += 1
            
            # 更新平均漂移分数
            total_divergence = metrics["avg_divergence"] * (metrics["total_episodes"] - 1) + report.divergence_score
            metrics["avg_divergence"] = total_divergence / metrics["total_episodes"]
            
            # 添加趋势数据
            metrics["trend_data"].append({
                "episode": report.episode_num,
                "divergence_score": report.divergence_score,
                "threshold": report.threshold,
                "status": report.status,
                "timestamp": report.timestamp.isoformat()
            })
            
            # 限制趋势数据长度
            if len(metrics["trend_data"]) > 100:
                metrics["trend_data"] = metrics["trend_data"][-100:]
            
            metrics["last_updated"] = datetime.now().isoformat()
            
            # 保存更新后的指标
            save_json(metrics, str(self.metrics_file))
            
        except Exception as e:
            logger.error(f"Failed to update metrics: {e}")
    
    def _add_to_yellow_queue(self, report: QualityReport) -> None:
        """添加到Yellow队列"""
        yellow_file = self.yellow_queue_dir / f"episode_{report.episode_num:02d}.md"
        
        markdown_content = self._generate_yellow_markdown(report)
        
        with open(yellow_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        logger.info(f"Added episode {report.episode_num} to yellow queue")
    
    def _generate_yellow_markdown(self, report: QualityReport) -> str:
        """生成Yellow队列Markdown"""
        content = f"""# YELLOW_ep{report.episode_num:02d}.md

## Episode {report.episode_num} Quality Issues

- **divergence**: {report.divergence_score:.3f} (threshold={report.threshold:.3f})
- **status**: {report.status.upper()}
- **severity**: {report.severity}
- **timestamp**: {report.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

### Missing Spine Events
"""
        
        if report.missing_spine_events:
            for event in report.missing_spine_events:
                content += f"- {event}\n"
        else:
            content += "- No missing spine events detected\n"
        
        content += "\n### Contradicted Events\n"
        
        if report.contradicted_events:
            for event in report.contradicted_events:
                content += f"- {event}\n"
        else:
            content += "- No contradicted events detected\n"
        
        content += "\n### Suggestions\n"
        
        if report.suggestions:
            for i, suggestion in enumerate(report.suggestions, 1):
                content += f"{i}. {suggestion}\n"
        else:
            content += "- No specific suggestions available\n"
        
        content += f"\n### Metadata\n"
        content += f"- **Auto-generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        content += f"- **Divergence Score**: {report.divergence_score:.3f}\n"
        content += f"- **Threshold**: {report.threshold:.3f}\n"
        
        return content
    
    def get_yellow_queue_status(self) -> Dict[str, Any]:
        """获取Yellow队列状态"""
        yellow_files = list(self.yellow_queue_dir.glob("*.md"))
        
        queue_items = []
        for file_path in yellow_files:
            # 从文件名提取集数
            episode_match = file_path.stem.split('_')[-1]
            try:
                episode_num = int(episode_match)
            except ValueError:
                continue
            
            # 获取文件修改时间
            mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
            
            queue_items.append({
                "episode_num": episode_num,
                "file_path": str(file_path),
                "created_at": mtime.isoformat(),
                "age_hours": (datetime.now() - mtime).total_seconds() / 3600
            })
        
        # 按集数排序
        queue_items.sort(key=lambda x: x["episode_num"])
        
        return {
            "total_items": len(queue_items),
            "items": queue_items,
            "oldest_item": min(queue_items, key=lambda x: x["age_hours"]) if queue_items else None,
            "newest_item": max(queue_items, key=lambda x: x["age_hours"]) if queue_items else None
        }
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """生成总结报告"""
        try:
            if not self.metrics_file.exists():
                return {"error": "No metrics data available"}
            
            metrics = load_json(str(self.metrics_file))
            yellow_status = self.get_yellow_queue_status()
            
            # 计算质量趋势
            trend_data = metrics.get("trend_data", [])
            if len(trend_data) >= 5:
                recent_scores = [item["divergence_score"] for item in trend_data[-5:]]
                trend = "improving" if recent_scores[-1] < recent_scores[0] else "declining"
            else:
                trend = "insufficient_data"
            
            # 计算各种比率
            total = metrics["total_episodes"]
            status_counts = metrics["status_counts"]
            
            summary = {
                "overview": {
                    "total_episodes": total,
                    "avg_divergence": round(metrics["avg_divergence"], 3),
                    "quality_trend": trend,
                    "last_updated": metrics["last_updated"]
                },
                "status_distribution": {
                    "green": {
                        "count": status_counts["green"],
                        "percentage": round(status_counts["green"] / total * 100, 1) if total > 0 else 0
                    },
                    "yellow": {
                        "count": status_counts["yellow"],
                        "percentage": round(status_counts["yellow"] / total * 100, 1) if total > 0 else 0
                    },
                    "red": {
                        "count": status_counts["red"],
                        "percentage": round(status_counts["red"] / total * 100, 1) if total > 0 else 0
                    }
                },
                "yellow_queue": yellow_status,
                "quality_metrics": {
                    "drift_rate": round(status_counts["yellow"] + status_counts["red"], 3) / total if total > 0 else 0,
                    "critical_rate": round(status_counts["red"] / total, 3) if total > 0 else 0
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate summary report: {e}")
            return {"error": str(e)}
    
    def plot_divergence_trends(self, save_path: Optional[str] = None) -> str:
        """绘制漂移趋势图"""
        try:
            if not self.metrics_file.exists():
                raise ValueError("No metrics data available")
            
            metrics = load_json(str(self.metrics_file))
            trend_data = metrics.get("trend_data", [])
            
            if len(trend_data) < 2:
                raise ValueError("Insufficient data for plotting")
            
            # 准备数据
            episodes = [item["episode"] for item in trend_data]
            divergence_scores = [item["divergence_score"] for item in trend_data]
            thresholds = [item["threshold"] for item in trend_data]
            
            # 创建图表
            plt.figure(figsize=(12, 6))
            plt.plot(episodes, divergence_scores, 'b-', label='Divergence Score', linewidth=2)
            plt.plot(episodes, thresholds, 'r--', label='Dynamic Threshold', linewidth=1)
            
            # 标记Yellow和Red区域
            for i, item in enumerate(trend_data):
                if item["status"] == "yellow":
                    plt.scatter(item["episode"], item["divergence_score"], color='orange', s=50, zorder=5)
                elif item["status"] == "red":
                    plt.scatter(item["episode"], item["divergence_score"], color='red', s=50, zorder=5)
            
            plt.xlabel('Episode Number')
            plt.ylabel('Divergence Score')
            plt.title('Quality Divergence Trends')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            
            # 保存图表
            if save_path is None:
                save_path = str(self.output_dir / "divergence_trends.png")
            
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Divergence trends plot saved to {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Failed to plot divergence trends: {e}")
            raise
    
    def export_metrics_csv(self, file_path: Optional[str] = None) -> str:
        """导出指标为CSV"""
        try:
            if not self.metrics_file.exists():
                raise ValueError("No metrics data available")
            
            metrics = load_json(str(self.metrics_file))
            trend_data = metrics.get("trend_data", [])
            
            if not trend_data:
                raise ValueError("No trend data available")
            
            # 转换为DataFrame
            df = pd.DataFrame(trend_data)
            
            # 添加计算列
            df['is_problematic'] = df['status'].isin(['yellow', 'red'])
            df['above_threshold'] = df['divergence_score'] > df['threshold']
            
            if file_path is None:
                file_path = str(self.output_dir / "quality_metrics.csv")
            
            df.to_csv(file_path, index=False)
            
            logger.info(f"Metrics exported to {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to export metrics: {e}")
            raise
    
    def cleanup_old_reports(self, max_age_days: int = 30) -> int:
        """清理旧的报告文件"""
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        deleted_count = 0
        
        # 清理质量报告
        for report_file in self.reports_dir.glob("*.json"):
            if datetime.fromtimestamp(report_file.stat().st_mtime) < cutoff_time:
                report_file.unlink()
                deleted_count += 1
        
        # 清理Yellow队列（已解决的）
        for yellow_file in self.yellow_queue_dir.glob("*.md"):
            if datetime.fromtimestamp(yellow_file.stat().st_mtime) < cutoff_time:
                yellow_file.unlink()
                deleted_count += 1
        
        logger.info(f"Cleaned up {deleted_count} old report files")
        return deleted_count
