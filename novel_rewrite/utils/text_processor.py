"""
文本处理工具
"""

import re
import unicodedata
import logging
from typing import Dict, List, Tuple, Optional
from opencc import OpenCC

logger = logging.getLogger(__name__)

class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        # 初始化繁简转换器
        try:
            self.cc = OpenCC('t2s')  # 繁体转简体
        except Exception as e:
            logger.warning(f"Failed to initialize OpenCC: {e}")
            self.cc = None
    
    def normalize_chinese_text(self, text: str) -> str:
        """标准化中文文本"""
        if not text:
            return text
        
        # Unicode标准化
        text = unicodedata.normalize('NFKC', text)
        
        # 繁体转简体
        if self.cc:
            try:
                text = self.cc.convert(text)
            except Exception as e:
                logger.warning(f"Failed to convert traditional to simplified: {e}")
        
        # 标点符号统一化
        text = self._normalize_punctuation(text)
        
        # 清理多余空白
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text
    
    def _normalize_punctuation(self, text: str) -> str:
        """标准化标点符号"""
        # 引号统一
        text = re.sub(r'[「『]', '"', text)
        text = re.sub(r'[」』]', '"', text)
        text = re.sub(r'[''']', "'", text)
        
        # 破折号统一
        text = re.sub(r'[—]{2,}', '——', text)
        text = re.sub(r'[-]{2,}', '——', text)
        
        # 省略号统一
        text = re.sub(r'[.]{3,}', '……', text)
        text = re.sub(r'[…]{2,}', '……', text)
        
        # 感叹号和问号
        text = re.sub(r'[！]{2,}', '！', text)
        text = re.sub(r'[？]{2,}', '？', text)
        
        return text
    
    def extract_dialogue_and_narration(self, script_text: str) -> Dict[str, List[str]]:
        """分离对话和旁白内容"""
        lines = script_text.split('\n')
        
        dialogue = []
        narration = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否为对话（角色名：内容）
            dialogue_match = re.match(r'^([一-龯]{2,4})[:：]\s*(.+)$', line)
            if dialogue_match:
                character, content = dialogue_match.groups()
                dialogue.append(f"{character}: {content}")
            else:
                # 其他内容视为旁白
                narration.append(line)
        
        return {
            "dialogue": dialogue,
            "narration": narration
        }
    
    def estimate_duration(self, script_text: str, tts_speed: int = 180) -> float:
        """估算TTS播放时长"""
        # 分离对话和旁白
        content = self.extract_dialogue_and_narration(script_text)
        
        total_chars = 0
        
        # 对话部分（语速稍慢）
        dialogue_chars = sum(len(line) for line in content['dialogue'])
        total_chars += dialogue_chars
        
        # 旁白部分（语速稍快）
        narration_chars = sum(len(line) for line in content['narration'])
        total_chars += narration_chars * 0.8  # 旁白语速快20%
        
        # 计算时长（分钟）
        duration_minutes = total_chars / tts_speed
        
        # 添加停顿时间（对话间隙、场景转换等）
        pause_time = len(content['dialogue']) * 0.5 / 60  # 每句对话0.5秒停顿
        
        return duration_minutes + pause_time
    
    def split_into_sentences(self, text: str) -> List[str]:
        """将文本分割为句子"""
        # 中文句子分割模式
        sentence_endings = r'[。！？…][""]?'
        sentences = re.split(sentence_endings, text)
        
        # 清理空句子和过短句子
        sentences = [s.strip() for s in sentences if len(s.strip()) > 3]
        
        return sentences
    
    def extract_character_names(self, text: str) -> List[str]:
        """从文本中提取可能的角色名"""
        # 中文人名模式
        name_patterns = [
            r'[一-龯]{2,4}(?=说|道|想|看|听|问|答|笑|哭|叫|喊|感到|决定)',
            r'^([一-龯]{2,4})[:：]',  # 对话格式
            r'([一-龯]{2,4})(?=的|地|得)',  # 所有格
        ]
        
        names = set()
        for pattern in name_patterns:
            matches = re.findall(pattern, text, re.MULTILINE)
            names.update(matches)
        
        # 过滤常见词汇
        common_words = {'这个', '那个', '什么', '怎么', '为什么', '哪里', '时候', '地方', '东西', '事情'}
        names = names - common_words
        
        return list(names)
    
    def clean_script_text(self, script_text: str) -> str:
        """清理剧本文本"""
        # 移除多余的空行
        script_text = re.sub(r'\n\s*\n', '\n\n', script_text)
        
        # 统一对话格式
        script_text = re.sub(r'^([一-龯]{2,4})\s*[:：]\s*', r'\1：', script_text, flags=re.MULTILINE)
        
        # 清理行首行尾空白
        lines = [line.strip() for line in script_text.split('\n')]
        script_text = '\n'.join(line for line in lines if line)
        
        return script_text
    
    def validate_script_format(self, script_text: str) -> List[str]:
        """验证剧本格式"""
        issues = []
        lines = script_text.split('\n')
        
        dialogue_count = 0
        narration_count = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            # 检查对话格式
            if re.match(r'^[一-龯]{2,4}[:：]', line):
                dialogue_count += 1
                # 检查对话内容是否为空
                content = line.split('：', 1)[-1].strip()
                if not content:
                    issues.append(f"第{i}行：对话内容为空")
            else:
                narration_count += 1
                # 检查旁白是否过长
                if len(line) > 200:
                    issues.append(f"第{i}行：旁白过长({len(line)}字符)")
        
        # 检查对话和旁白比例
        total_lines = dialogue_count + narration_count
        if total_lines > 0:
            dialogue_ratio = dialogue_count / total_lines
            if dialogue_ratio < 0.3:
                issues.append(f"对话比例过低：{dialogue_ratio:.1%}")
            elif dialogue_ratio > 0.8:
                issues.append(f"对话比例过高：{dialogue_ratio:.1%}")
        
        return issues

def standardize_character_names(text: str, alias_map: Dict[str, List[str]]) -> str:
    """标准化文本中的角色名称"""
    if not alias_map:
        return text
    
    # 构建反向映射
    reverse_map = {}
    for canonical, aliases in alias_map.items():
        reverse_map[canonical] = canonical
        for alias in aliases:
            reverse_map[alias] = canonical
    
    # 按长度排序，先替换长的别名
    sorted_aliases = sorted(reverse_map.keys(), key=len, reverse=True)
    
    result = text
    for alias in sorted_aliases:
        canonical = reverse_map[alias]
        if alias != canonical:
            # 使用词边界匹配
            pattern = r'\b' + re.escape(alias) + r'\b'
            result = re.sub(pattern, canonical, result)
    
    return result

def extract_key_phrases(text: str, max_phrases: int = 10) -> List[str]:
    """提取关键短语"""
    # 简单的关键短语提取
    phrases = []
    
    # 提取动词短语
    verb_patterns = [
        r'[一-龯]{2,4}(决定|选择|发现|攻击|保护|拯救)[一-龯\s]{2,10}',
        r'[一-龯]{2,4}(对|向|朝)[一-龯]{2,4}(说|道|喊)',
        r'[一-龯]{2,4}(看到|听到|感受到)[一-龯\s]{2,10}'
    ]
    
    for pattern in verb_patterns:
        matches = re.findall(pattern, text)
        phrases.extend(matches)
    
    # 去重并限制数量
    phrases = list(set(phrases))[:max_phrases]
    
    return phrases
