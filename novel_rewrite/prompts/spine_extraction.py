"""
Spine事件抽取提示词生成器
"""

from typing import Dict, List, Any

def get_spine_extraction_prompt(chapter_summary: Dict[str, Any], alias_map: Dict[str, List[str]], max_events: int = 4) -> str:
    """生成Spine事件抽取提示词"""
    
    # 构建角色别名信息
    alias_info = ""
    if alias_map:
        alias_info = "## 角色别名映射\n"
        for canonical, aliases in alias_map.items():
            if aliases:
                alias_info += f"- {canonical}: {', '.join(aliases)}\n"
        alias_info += "\n请在输出中使用标准角色名称。\n\n"
    
    # 提取章节基本信息
    chapter_num = chapter_summary.get('basic_info', {}).get('chapter_number', 'unknown')
    chapter_title = chapter_summary.get('basic_info', {}).get('title', '')
    
    # 提取章节内容
    narrative_content = chapter_summary.get('narrative', {}).get('content', '')
    key_events = chapter_summary.get('key_events', [])
    key_characters = chapter_summary.get('key_characters', [])
    
    # 构建事件信息
    events_info = ""
    if key_events:
        events_info = "### 关键事件\n"
        for i, event in enumerate(key_events, 1):
            event_text = event.get('event', '') or event.get('details', '')
            events_info += f"{i}. {event_text}\n"
        events_info += "\n"
    
    # 构建角色信息
    characters_info = ""
    if key_characters:
        characters_info = "### 主要角色\n"
        characters_info += ", ".join(key_characters) + "\n\n"
    
    prompt = f"""# Spine事件抽取任务

你是一个专业的小说分析师，需要从章节摘要中抽取关键的Spine事件。这些事件将作为后续剧本生成的锚点，确保改编内容不偏离原著。

{alias_info}

## 章节信息
- **章节**: 第{chapter_num}章 {chapter_title}
- **最大事件数**: {max_events}

{characters_info}

### 章节摘要
{narrative_content}

{events_info}

## 抽取要求

### 事件类型定义
- **plot**: 情节推进事件（角色行动、决策、冲突、发现等）
- **emotion**: 情感转折事件（角色心理变化、关系变化、情感波动等）

### 评分标准
**重要性 (importance: 0-1)**
- 0.9-1.0: 对整体剧情发展至关重要
- 0.7-0.8: 重要的情节转折或角色发展  
- 0.5-0.6: 有意义但非关键的事件
- 0.3-0.4: 次要事件

**张力 (tension: 0-1)**
- 0.8-1.0: 高度紧张、冲突激烈
- 0.6-0.7: 中等张力，有明显冲突或悬念
- 0.4-0.5: 轻微张力或情感波动
- 0.2-0.3: 相对平静

### 筛选规则
1. 按importance优先选择前2个事件
2. 如果章节整体张力较高，可添加1-2个高tension事件
3. 总数不超过{max_events}个
4. 每个事件描述控制在25个token以内（约100字符）

### 质量要求
- 必须基于提供的章节内容，不得添加原文没有的元素
- 使用标准化的角色名称
- 保持事件的逻辑连贯性
- 突出核心动作和结果，避免过度细节

## 输出格式
请严格按照以下JSON格式输出：

```json
{{
  "spine_events": [
    {{
      "id": "{chapter_num}-1",
      "type": "plot",
      "text": "简洁的事件描述",
      "importance": 0.85,
      "tension": 0.6
    }}
  ]
}}
```

请开始分析并抽取Spine事件："""

    return prompt

def get_spine_validation_prompt(spine_events: List[Dict[str, Any]], chapter_summary: Dict[str, Any]) -> str:
    """生成Spine事件验证提示词"""
    
    chapter_num = chapter_summary.get('basic_info', {}).get('chapter_number', 'unknown')
    narrative_content = chapter_summary.get('narrative', {}).get('content', '')
    
    events_text = ""
    for event in spine_events:
        events_text += f"- {event['id']}: {event['text']} (重要性: {event['importance']}, 张力: {event['tension']})\n"
    
    prompt = f"""# Spine事件验证任务

请验证以下抽取的Spine事件是否准确反映了章节内容。

## 章节信息
- **章节**: 第{chapter_num}章
- **章节摘要**: {narrative_content}

## 抽取的Spine事件
{events_text}

## 验证要点
1. 事件是否确实在章节中发生？
2. 重要性和张力评分是否合理？
3. 事件描述是否准确简洁？
4. 是否遗漏了更重要的事件？

## 输出格式
```json
{{
  "validation_result": {{
    "overall_quality": "excellent|good|fair|poor",
    "issues": [
      "具体问题描述"
    ],
    "suggestions": [
      "改进建议"
    ],
    "missing_events": [
      "可能遗漏的重要事件"
    ]
  }}
}}
```

请进行验证："""

    return prompt
