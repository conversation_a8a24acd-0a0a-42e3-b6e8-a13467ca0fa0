"""
质量评审提示词生成器
"""

from typing import Dict, List, Any, Optional

def get_quality_review_prompt(
    episode_script: str,
    episode_structure: Dict[str, Any],
    spine_events: List[str],
    episode_number: int
) -> str:
    """生成质量评审提示词"""
    
    spine_events_text = "\n".join(f"- {event}" for event in spine_events)
    scenes_count = len(episode_structure.get("scenes", []))
    target_duration = episode_structure.get("estimated_duration", 12.0)
    
    prompt = f"""# 剧本质量评审任务

你是一个专业的剧本质量评审师，需要对第{episode_number}集的剧本进行全面评审。

## 评审对象
**集数**: 第{episode_number}集
**目标场景数**: {scenes_count}
**目标时长**: {target_duration}分钟

## 必须覆盖的Spine事件
{spine_events_text}

## 剧本内容
```
{episode_script}
```

## 评审维度

### 1. 结构完整性 (30分)
- 是否包含了所有要求的场景？
- 场景转换是否自然流畅？
- 整体结构是否符合起承转合？
- 时长分配是否合理？

### 2. Spine事件覆盖 (25分)
- 所有必需的Spine事件是否都有体现？
- 事件的呈现方式是否合理？
- 是否保持了事件的原始意图？
- 有无添加原本不存在的重大情节？

### 3. 角色塑造 (20分)
- 角色对话是否符合人物设定？
- 角色行为是否逻辑一致？
- 角色关系发展是否合理？
- 是否体现了角色的成长变化？

### 4. 对话质量 (15分)
- 对话是否自然流畅？
- 是否符合时代背景和世界观？
- 对话是否推进剧情发展？
- 语言风格是否统一？

### 5. 技术规范 (10分)
- 剧本格式是否标准？
- 场景描述是否清晰？
- 旁白是否适度？
- 标点符号使用是否正确？

## 评审输出格式

请按以下JSON格式输出评审结果：

```json
{{
  "overall_score": 85,
  "dimension_scores": {{
    "structure": 26,
    "spine_coverage": 22,
    "character": 18,
    "dialogue": 13,
    "technical": 9
  }},
  "strengths": [
    "场景转换自然流畅",
    "角色对话符合人物设定",
    "Spine事件覆盖完整"
  ],
  "weaknesses": [
    "第3场景时长过长",
    "部分对话略显生硬",
    "缺少某个关键情节点"
  ],
  "specific_issues": [
    {{
      "scene": 2,
      "issue": "角色A的对话与前面设定不符",
      "severity": "medium",
      "suggestion": "修改为更符合角色性格的表达方式"
    }},
    {{
      "scene": 4,
      "issue": "场景转换过于突兀",
      "severity": "low", 
      "suggestion": "添加过渡性的旁白或对话"
    }}
  ],
  "improvement_suggestions": [
    "压缩第3场景的对话，控制在2.5分钟内",
    "增强角色B的情感表达",
    "优化场景4的转场设计"
  ],
  "spine_coverage_analysis": {{
    "covered_events": ["事件1", "事件2"],
    "missing_events": ["事件3"],
    "coverage_rate": 0.67
  }},
  "estimated_duration": 13.2,
  "quality_level": "good"
}}
```

## 评分标准

**优秀 (90-100分)**: 各方面表现出色，基本无需修改
**良好 (80-89分)**: 整体质量较高，有少量需要优化的地方  
**合格 (70-79分)**: 基本达到要求，但有明显的改进空间
**需要改进 (60-69分)**: 存在较多问题，需要大幅修改
**不合格 (<60分)**: 质量较差，建议重新生成

## 注意事项

1. **客观评审**: 基于具体标准进行评分，避免主观偏见
2. **建设性反馈**: 指出问题的同时提供具体的改进建议
3. **平衡考虑**: 在忠实原著和戏剧效果之间找到平衡点
4. **实用性**: 建议应该具体可操作，便于编剧修改

请开始对第{episode_number}集剧本进行详细评审："""

    return prompt

def get_spine_coverage_check_prompt(
    episode_script: str,
    required_spine_events: List[str],
    episode_number: int
) -> str:
    """生成Spine事件覆盖检查提示词"""
    
    spine_list = "\n".join(f"{i+1}. {event}" for i, event in enumerate(required_spine_events))
    
    prompt = f"""# Spine事件覆盖检查

请检查第{episode_number}集剧本是否完整覆盖了所有必需的Spine事件。

## 必需的Spine事件
{spine_list}

## 剧本内容
```
{episode_script}
```

## 检查要求

对每个Spine事件，请确认：
1. 是否在剧本中有明确体现？
2. 体现的方式是否合理？
3. 是否保持了事件的核心意图？

## 输出格式

```json
{{
  "coverage_analysis": [
    {{
      "spine_event": "事件1描述",
      "covered": true,
      "location": "场景2",
      "coverage_quality": "excellent",
      "notes": "通过角色对话很好地体现了这个事件"
    }},
    {{
      "spine_event": "事件2描述", 
      "covered": false,
      "location": null,
      "coverage_quality": "missing",
      "notes": "在剧本中完全没有体现"
    }}
  ],
  "overall_coverage_rate": 0.75,
  "missing_events": ["事件2描述"],
  "recommendations": [
    "在场景3中添加事件2的相关内容",
    "可以通过角色回忆的方式补充缺失事件"
  ]
}}
```

请开始检查："""

    return prompt

def get_dialogue_quality_check_prompt(
    episode_script: str,
    character_profiles: Dict[str, str],
    world_setting: str = "medieval_fantasy"
) -> str:
    """生成对话质量检查提示词"""
    
    characters_info = ""
    if character_profiles:
        characters_info = "## 角色设定\n"
        for char, profile in character_profiles.items():
            characters_info += f"**{char}**: {profile}\n"
        characters_info += "\n"
    
    prompt = f"""# 对话质量检查

请检查剧本中对话的质量，重点关注角色一致性和语言风格。

{characters_info}

## 世界观设定
{world_setting}

## 剧本内容
```
{episode_script}
```

## 检查维度

### 1. 角色一致性
- 对话是否符合角色性格？
- 说话方式是否与角色身份匹配？
- 角色间的对话关系是否合理？

### 2. 语言风格
- 是否符合世界观设定？
- 用词是否得当？
- 语言风格是否统一？

### 3. 功能性
- 对话是否推进剧情？
- 是否有效传达信息？
- 是否增进角色关系？

## 输出格式

```json
{{
  "dialogue_analysis": [
    {{
      "character": "角色名",
      "line": "具体对话内容",
      "scene": 1,
      "issues": ["问题描述"],
      "suggestions": ["改进建议"]
    }}
  ],
  "overall_quality": "good",
  "style_consistency": 0.85,
  "character_consistency": 0.90,
  "general_recommendations": [
    "整体建议1",
    "整体建议2"
  ]
}}
```

请开始检查："""

    return prompt
