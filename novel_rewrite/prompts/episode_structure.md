# 剧集结构生成提示词模板

## 任务描述
基于Spine事件和前一集的结束状态，生成详细的剧集结构，包含5-6个场景的完整布局。

## 核心约束

### Spine事件覆盖
- 所有提供的spine_events必须在剧本中出现
- 保持事件的原始走向和因果关系
- 可以调整事件的呈现方式，但不能改变核心结果

### 连续性要求
- 必须与previous_end_state保持连续性
- 角色状态变化要合理渐进
- 未解决的悬念要有所回应或发展
- 世界设定变化要保持一致

### 压缩比控制
- 根据compression_ratio调整内容密度
- 0.3: 保留大部分原著细节，适度压缩
- 0.5: 平衡压缩，保留核心情节
- 0.7: 高度压缩，快节奏推进

## 结构要求

### 场景数量
- 标准：5-6个场景
- 每个场景时长：2-3分钟
- 总时长控制：10-15分钟

### 场景要素
每个场景必须包含：
1. **环境设定**: 时间、地点、氛围
2. **角色配置**: 主要角色及其状态
3. **核心冲突**: 主要矛盾或推进点
4. **情感走向**: 角色情感变化
5. **转场设置**: 与下一场景的连接

### 节奏控制
- 开场：快速建立情境和冲突
- 发展：逐步推进，保持张力
- 高潮：集中处理主要冲突
- 结尾：解决部分问题，设置新悬念

## 改编原则

### 忠实度要求
- 不得引入Spine事件之外的高能科技
- 不得添加原著没有的世界观要素
- 角色性格和动机要保持一致
- 重要的因果关系不能改变

### 创作自由度
- 可以合并或拆分场景
- 可以调整对话和细节描述
- 可以增删次要角色的戏份
- 可以调整事件的呈现顺序（在合理范围内）

## 输出格式

```json
{
  "episode_structure": {
    "episode_number": 1,
    "total_scenes": 5,
    "estimated_duration": 12.5,
    "spine_coverage": ["12-1", "12-2"],
    "scenes": [
      {
        "scene_number": 1,
        "title": "场景标题",
        "location": "地点描述",
        "time": "时间设定",
        "characters": ["角色1", "角色2"],
        "main_conflict": "核心冲突描述",
        "emotional_arc": "情感变化",
        "key_events": ["事件1", "事件2"],
        "spine_events_covered": ["12-1"],
        "transition": "转场描述"
      }
    ]
  },
  "end_state": {
    "char_states": {
      "角色名": {
        "goal": "目标",
        "risk": "风险", 
        "emotion": "情感"
      }
    },
    "open_hooks": ["悬念1", "悬念2"],
    "world_delta": ["变化1"]
  }
}
```

## 质量检查点
1. 所有spine_events是否都被覆盖？
2. 与previous_end_state的连续性是否合理？
3. 场景转换是否自然流畅？
4. 角色发展是否符合逻辑？
5. 时长估算是否在目标范围内？
