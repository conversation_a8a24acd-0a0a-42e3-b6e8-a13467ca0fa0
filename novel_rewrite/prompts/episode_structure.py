"""
剧集结构生成提示词生成器
"""

from typing import Dict, List, Any, Optional

def get_episode_structure_prompt(
    spine_events: List[Dict[str, Any]], 
    previous_end_state: Optional[Dict[str, Any]] = None,
    compression_ratio: float = 0.3,
    episode_number: int = 1,
    target_duration: float = 12.0
) -> str:
    """生成剧集结构提示词"""
    
    # 构建Spine事件信息
    spine_info = "## Spine事件（必须覆盖）\n"
    for event in spine_events:
        spine_info += f"- **{event['id']}** ({event['type']}): {event['text']}\n"
        spine_info += f"  - 重要性: {event['importance']}, 张力: {event['tension']}\n"
    spine_info += "\n"
    
    # 构建前一集状态信息
    previous_state_info = ""
    if previous_end_state:
        previous_state_info = "## 前一集结束状态\n"
        
        # 角色状态
        if 'char_states' in previous_end_state:
            previous_state_info += "### 角色状态\n"
            for char, state in previous_end_state['char_states'].items():
                previous_state_info += f"- **{char}**: 目标({state.get('goal', '未知')}) | 风险({state.get('risk', '未知')}) | 情感({state.get('emotion', '未知')})\n"
        
        # 未解决悬念
        if 'open_hooks' in previous_end_state:
            previous_state_info += "\n### 未解决悬念\n"
            for hook in previous_end_state['open_hooks']:
                previous_state_info += f"- {hook}\n"
        
        # 世界变化
        if 'world_delta' in previous_end_state:
            previous_state_info += "\n### 世界变化\n"
            for delta in previous_end_state['world_delta']:
                previous_state_info += f"- {delta}\n"
        
        previous_state_info += "\n"
    
    # 压缩比说明
    compression_guide = {
        0.3: "保留大部分原著细节，适度压缩。允许较多的角色互动和环境描述。",
        0.5: "平衡压缩，保留核心情节。专注于主要角色和关键事件。",
        0.7: "高度压缩，快节奏推进。只保留最核心的情节和对话。"
    }
    
    # 找到最接近的压缩比指导
    closest_ratio = min(compression_guide.keys(), key=lambda x: abs(x - compression_ratio))
    compression_instruction = compression_guide[closest_ratio]
    
    prompt = f"""# 剧集结构生成任务

你是一个专业的剧本结构师，需要为第{episode_number}集生成详细的剧集结构。

{spine_info}

{previous_state_info}

## 生成参数
- **集数**: 第{episode_number}集
- **压缩比**: {compression_ratio} - {compression_instruction}
- **目标时长**: {target_duration}分钟
- **场景数量**: 5-6个场景

## 核心要求

### 1. Spine事件覆盖（强制约束）
- 所有列出的spine_events必须在剧集中出现
- 保持事件的原始走向和因果关系
- 可以调整呈现方式，但不能改变核心结果
- 不得引入spine_events之外的重大情节元素

### 2. 连续性保持
- 必须与前一集的结束状态保持逻辑连续性
- 角色状态变化要合理渐进，不能突然跳跃
- 对未解决悬念要有所回应或进一步发展
- 世界设定变化要保持一致性

### 3. 结构设计
- **开场** (场景1): 快速建立当前情境，回应前集悬念
- **发展** (场景2-4): 逐步推进spine事件，保持张力递增
- **高潮** (场景5): 处理本集的主要冲突或转折
- **结尾** (场景6): 解决部分问题，设置新的悬念

### 4. 场景要求
每个场景必须包含：
- 明确的时间、地点、氛围设定
- 主要角色及其当前状态
- 核心冲突或推进点
- 角色情感变化轨迹
- 与下一场景的自然转换

### 5. 改编原则
**允许的创作自由**:
- 合并或拆分原著场景
- 调整对话和细节描述
- 增删次要角色戏份
- 在合理范围内调整事件顺序

**严格禁止**:
- 引入原著没有的高科技元素
- 添加不符合世界观的设定
- 改变重要角色的核心性格
- 破坏关键的因果关系链

## 输出格式

请严格按照以下JSON格式输出：

```json
{{
  "episode_structure": {{
    "episode_number": {episode_number},
    "total_scenes": 5,
    "estimated_duration": {target_duration},
    "compression_ratio": {compression_ratio},
    "spine_coverage": ["事件ID列表"],
    "scenes": [
      {{
        "scene_number": 1,
        "title": "场景标题",
        "location": "具体地点描述",
        "time": "时间设定（相对或绝对）",
        "characters": ["主要角色列表"],
        "main_conflict": "本场景的核心冲突或推进点",
        "emotional_arc": "角色情感变化描述",
        "key_events": ["本场景发生的关键事件"],
        "spine_events_covered": ["覆盖的spine事件ID"],
        "transition": "与下一场景的转换方式",
        "estimated_duration": 2.5
      }}
    ]
  }},
  "end_state": {{
    "char_states": {{
      "角色名": {{
        "goal": "当前目标（≤10字）",
        "risk": "面临风险（≤10字）",
        "emotion": "情感状态（≤10字）"
      }}
    }},
    "open_hooks": ["本集结束时的未解悬念"],
    "world_delta": ["本集产生的世界变化"]
  }}
}}
```

## 质量自检
生成结构后，请确认：
1. ✓ 所有spine_events都被覆盖
2. ✓ 与previous_end_state连续性合理
3. ✓ 场景转换自然流畅
4. ✓ 角色发展符合逻辑
5. ✓ 总时长在目标范围内
6. ✓ 没有引入原著外的元素

请开始生成第{episode_number}集的剧集结构："""

    return prompt

def get_structure_refinement_prompt(
    initial_structure: Dict[str, Any],
    feedback: List[str],
    spine_events: List[Dict[str, Any]]
) -> str:
    """生成结构优化提示词"""
    
    episode_num = initial_structure.get('episode_number', 1)
    
    feedback_text = ""
    if feedback:
        feedback_text = "## 反馈意见\n"
        for i, fb in enumerate(feedback, 1):
            feedback_text += f"{i}. {fb}\n"
        feedback_text += "\n"
    
    spine_coverage = initial_structure.get('spine_coverage', [])
    missing_spines = [event['id'] for event in spine_events if event['id'] not in spine_coverage]
    
    missing_info = ""
    if missing_spines:
        missing_info = f"## 遗漏的Spine事件\n"
        for spine_id in missing_spines:
            spine_event = next((e for e in spine_events if e['id'] == spine_id), None)
            if spine_event:
                missing_info += f"- {spine_id}: {spine_event['text']}\n"
        missing_info += "\n"
    
    prompt = f"""# 剧集结构优化任务

请根据反馈意见优化第{episode_num}集的剧集结构。

## 当前结构
```json
{initial_structure}
```

{feedback_text}

{missing_info}

## 优化要求
1. 解决所有反馈中提到的问题
2. 确保所有spine事件都被覆盖
3. 保持结构的整体连贯性
4. 维持目标时长和场景数量

请输出优化后的完整剧集结构（使用相同的JSON格式）："""

    return prompt
