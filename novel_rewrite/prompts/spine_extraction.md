# Spine事件抽取提示词模板

## 任务描述
从章节摘要中抽取1-4个关键的Spine事件，这些事件将作为后续剧本生成的锚点。

## 核心要求

### 事件类型
- **plot**: 情节推进事件（角色行动、决策、冲突等）
- **emotion**: 情感转折事件（角色心理变化、关系变化等）

### 重要性评分 (importance: 0-1)
- **0.9-1.0**: 对整体剧情发展至关重要的事件
- **0.7-0.8**: 重要的情节转折或角色发展
- **0.5-0.6**: 有意义但非关键的事件
- **0.3-0.4**: 次要事件

### 张力评分 (tension: 0-1)
- **0.8-1.0**: 高度紧张、冲突激烈的时刻
- **0.6-0.7**: 中等张力，有明显冲突或悬念
- **0.4-0.5**: 轻微张力或情感波动
- **0.2-0.3**: 相对平静的时刻

## 筛选规则
1. 按importance优先选择前2个事件
2. 如果章节整体tension > 0.7，再添加1-2个高tension事件
3. 总数不超过4个事件
4. 每个事件描述不超过25个token（约100字符）

## 输出格式
```json
{
  "spine_events": [
    {
      "id": "章节号-序号",
      "type": "plot|emotion", 
      "text": "简洁的事件描述",
      "importance": 0.0-1.0,
      "tension": 0.0-1.0
    }
  ]
}
```

## 注意事项
- 必须基于原文内容，不得添加原文没有的元素
- 使用标准化的角色名称（根据alias_map）
- 保持事件的因果逻辑关系
- 避免过于详细的描述，重点突出核心动作和结果
