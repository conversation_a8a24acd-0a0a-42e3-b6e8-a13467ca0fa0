"""
剧本生成提示词生成器
"""

from typing import Dict, List, Any, Optional

def get_script_generation_prompt(
    episode_structure: Dict[str, Any],
    compression_ratio: float = 0.3,
    style: str = "faithful",
    previous_episode_script: Optional[str] = None,
    world_tone: str = "medieval_fantasy"
) -> str:
    """生成剧本生成提示词"""
    
    episode_num = episode_structure.get("episode_number", 1)
    scenes = episode_structure.get("scenes", [])
    spine_coverage = episode_structure.get("spine_coverage", [])
    
    # 风格指导
    style_guides = {
        "faithful": "忠实还原原著内容，保持原有的节奏和氛围。",
        "punchy": "快节奏推进，增强戏剧张力，但不偏离核心情节。",
        "balanced": "在忠实原著和戏剧效果之间找到平衡。"
    }
    
    style_instruction = style_guides.get(style, style_guides["faithful"])
    
    # 压缩比指导
    compression_guides = {
        0.3: "保留大部分细节和对话，适度压缩冗余内容。",
        0.5: "保留核心情节和关键对话，压缩次要内容。", 
        0.7: "高度压缩，只保留最核心的情节推进和关键对话。"
    }
    
    closest_ratio = min(compression_guides.keys(), key=lambda x: abs(x - compression_ratio))
    compression_instruction = compression_guides[closest_ratio]
    
    # 构建场景信息
    scenes_info = ""
    for i, scene in enumerate(scenes, 1):
        scenes_info += f"""
### 场景{i}: {scene.get('title', f'场景{i}')}
- **地点**: {scene.get('location', '未指定')}
- **时间**: {scene.get('time', '未指定')}
- **主要角色**: {', '.join(scene.get('characters', []))}
- **核心冲突**: {scene.get('main_conflict', '推进剧情')}
- **情感走向**: {scene.get('emotional_arc', '平稳发展')}
- **关键事件**: {', '.join(scene.get('key_events', []))}
- **覆盖的Spine事件**: {', '.join(scene.get('spine_events_covered', []))}
- **预估时长**: {scene.get('estimated_duration', 2.5)}分钟
"""
    
    # 前一集信息
    previous_context = ""
    if previous_episode_script:
        previous_context = f"""
## 前一集剧本参考
以下是前一集的部分内容，用于保持连续性：

```
{previous_episode_script[-500:]}  # 只显示最后500字符
```

请确保本集开头与前一集结尾自然衔接。
"""
    
    prompt = f"""# 剧本生成任务

你是一个专业的剧本编剧，需要根据提供的剧集结构生成完整的剧本。

## 基本信息
- **集数**: 第{episode_num}集
- **总场景数**: {len(scenes)}
- **预估总时长**: {episode_structure.get('estimated_duration', 12.0)}分钟
- **压缩比**: {compression_ratio} - {compression_instruction}
- **风格要求**: {style} - {style_instruction}
- **世界观**: {world_tone}

## 必须覆盖的Spine事件
{', '.join(spine_coverage) if spine_coverage else '无特定要求'}

{previous_context}

## 剧集结构
{scenes_info}

## 剧本生成要求

### 1. 格式规范
- 使用标准的剧本格式
- 角色对话格式：`角色名：对话内容`
- 旁白格式：`旁白：场景描述或动作描述`
- 场景标题格式：`## 场景X：地点 - 时间`

### 2. 内容要求
- **忠实性**: 严格基于提供的剧集结构，不得添加结构中没有的重大情节
- **连贯性**: 场景之间转换自然，逻辑清晰
- **角色一致性**: 保持角色性格和说话方式的一致性
- **时长控制**: 每个场景控制在预估时长范围内

### 3. 对话要求
- 符合角色身份和时代背景
- 推进情节发展，避免无意义的闲聊
- 保持自然流畅，避免过于生硬
- 适当使用古风用词，但不要过于晦涩

### 4. 旁白要求
- 简洁明了，重点描述关键动作和场景变化
- 避免过度描述，给演员和导演留出发挥空间
- 注重视觉效果的描述

### 5. 节奏控制
- 根据压缩比调整内容密度
- 保持适当的张弛有度
- 重要情节给予充分展现，次要内容适度压缩

### 6. 质量标准
- 每个场景必须推进整体剧情
- 对话要有层次，体现角色关系变化
- 场景结尾要有适当的转场设置
- 整集要有完整的起承转合

## 输出格式

请按以下格式输出完整剧本：

```
# 第{episode_num}集

## 场景1：[地点] - [时间]

旁白：[场景设定和环境描述]

角色A：[对话内容]

角色B：[对话内容]

旁白：[动作描述]

[继续对话和动作...]

## 场景2：[地点] - [时间]

[继续下一个场景...]
```

## 质量自检

生成剧本后，请确认：
1. ✓ 所有场景都已包含且符合结构要求
2. ✓ 预估时长在目标范围内
3. ✓ 对话自然流畅，符合角色特点
4. ✓ 情节推进合理，没有逻辑漏洞
5. ✓ 场景转换自然，整体连贯
6. ✓ 没有添加结构外的重大情节元素

请开始生成第{episode_num}集的完整剧本："""

    return prompt

def get_script_refinement_prompt(
    original_script: str,
    review_feedback: List[str],
    episode_structure: Dict[str, Any]
) -> str:
    """生成剧本优化提示词"""
    
    episode_num = episode_structure.get("episode_number", 1)
    
    feedback_text = ""
    if review_feedback:
        feedback_text = "## 修改要求\n"
        for i, feedback in enumerate(review_feedback, 1):
            feedback_text += f"{i}. {feedback}\n"
        feedback_text += "\n"
    
    prompt = f"""# 剧本优化任务

请根据反馈意见优化第{episode_num}集的剧本。

## 原始剧本
```
{original_script}
```

{feedback_text}

## 优化要求
1. 解决所有反馈中提到的问题
2. 保持剧本的整体结构和核心情节
3. 确保修改后的内容符合角色设定
4. 维持场景间的逻辑连贯性
5. 保持目标时长不变

## 输出要求
请输出完整的优化后剧本，使用相同的格式。对于修改的部分，请在旁边用注释说明修改原因。

优化后的剧本："""

    return prompt
