# novel_rewrite/bootstrap_spec.yaml

project:
  name: "novel_rewrite"
  version: "2.0.0"
  description: "智能小说改编剧本生成系统"
  
architecture:
  core_modules:
    - spine: "Key-Event Spine事件抽取"
    - alias: "角色别名管理"
    - phase: "动态Phase分配"
    - drift: "剧情漂移检测"
    - end_state: "跨集状态管理"
    - models: "Pydantic数据模型"
  
  io_modules:
    - load_save: "统一I/O操作"
    - vector_cache: "向量缓存管理"
    - github_bot: "GitHub集成"
  
  pipeline_modules:
    - build_spine: "Spine构建流程"
    - build_phases: "Phase分配流程"
    - build_episodes: "剧集生成流程"
    - full_pipeline: "完整处理流程"

quality_targets:
  drift_rate: 0.15      # 剧情漂移率 <15%
  manual_edit_rate: 0.20 # 人工改动率 <20%
  length_error: 0.15     # 时长误差 ±15%
  processing_speed: 600  # 处理速度 <10min/1k章

dependencies:
  llm_models:
    - "xiaobu-sbert-base"
    - "mxbai-large"
  
  external_apis:
    - "OpenAI GPT-4"
    - "GitHub API v4"
  
  storage:
    - "SQLite (vector cache)"
    - "JSON (配置文件)"

monitoring:
  metrics:
    - "divergence_score"
    - "spine_coverage"
    - "yellow_queue_size"
    - "processing_time"
  
  alerts:
    - "连续3集Yellow"
    - "漂移率>20%"
    - "处理时间>15min"
