"""
Pydantic数据模型定义
"""

from pydantic import BaseModel, Field, validator
from typing import Dict, List, Optional, Literal, Any
from datetime import datetime
import hashlib
import json

class SpineEvent(BaseModel):
    """Spine事件数据模型"""
    id: str = Field(..., description="事件唯一标识")
    type: Literal["plot", "emotion"] = Field(..., description="事件类型")
    text: str = Field(..., max_length=100, description="事件描述文本")
    importance: float = Field(..., ge=0, le=1, description="事件重要性(0-1)")
    tension: float = Field(..., ge=0, le=1, description="事件张力(0-1)")
    
    @validator('text')
    def validate_text_length(cls, v):
        if len(v) > 100:  # ~25 tokens
            raise ValueError(f"事件描述过长: {len(v)} 字符")
        return v

class CharacterState(BaseModel):
    """角色状态模型"""
    goal: str = Field(..., max_length=10, description="角色目标")
    risk: str = Field(..., max_length=10, description="角色风险")
    emotion: str = Field(..., max_length=10, description="角色情感")

class EndState(BaseModel):
    """集结束状态模型"""
    char_states: Dict[str, CharacterState] = Field(..., description="角色状态字典")
    open_hooks: List[str] = Field(..., max_items=5, description="未解决的悬念")
    world_delta: List[str] = Field(..., max_items=3, description="世界变化")
    checksum: Optional[str] = Field(None, description="状态校验和")
    
    def __init__(self, **data):
        super().__init__(**data)
        if not self.checksum:
            self.checksum = self._calculate_checksum()
    
    def _calculate_checksum(self) -> str:
        """计算状态校验和"""
        data = {
            "char_states": {k: v.dict() for k, v in self.char_states.items()},
            "open_hooks": sorted(self.open_hooks),
            "world_delta": sorted(self.world_delta)
        }
        return hashlib.sha256(json.dumps(data, sort_keys=True).encode()).hexdigest()[:16]

class QualityReport(BaseModel):
    """质量报告模型"""
    episode_num: int = Field(..., description="集数")
    divergence_score: float = Field(..., ge=0, le=1, description="漂移分数")
    threshold: float = Field(..., ge=0, le=1, description="阈值")
    status: Literal["green", "yellow", "red"] = Field(..., description="状态")
    missing_spine_events: List[str] = Field(default_factory=list, description="缺失的Spine事件")
    contradicted_events: List[str] = Field(default_factory=list, description="矛盾的事件")
    suggestions: List[str] = Field(default_factory=list, description="修改建议")
    severity: Literal["LOW", "MEDIUM", "HIGH"] = Field(..., description="严重程度")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

class EpisodeMetrics(BaseModel):
    """集数指标模型"""
    episode_num: int = Field(..., description="集数")
    scene_count: int = Field(..., ge=1, description="场景数量")
    estimated_duration: float = Field(..., gt=0, description="预估时长(分钟)")
    character_count: int = Field(..., ge=0, description="角色数量")
    dialogue_ratio: float = Field(..., ge=0, le=1, description="对话占比")
    spine_coverage: float = Field(..., ge=0, le=1, description="Spine事件覆盖率")

class PhaseAllocation(BaseModel):
    """阶段分配模型"""
    setup: List[str] = Field(default_factory=list, description="设置阶段组")
    development: List[str] = Field(default_factory=list, description="发展阶段组")
    climax: List[str] = Field(default_factory=list, description="高潮阶段组")
    resolution: List[str] = Field(default_factory=list, description="结局阶段组")
    
    @validator('setup')
    def validate_setup_not_empty(cls, v):
        if not v:
            raise ValueError("设置阶段不能为空")
        return v

class ProcessingConfig(BaseModel):
    """处理配置模型"""
    compression_ratio: float = Field(0.3, ge=0, le=1, description="压缩比例")
    max_episodes: Optional[int] = Field(None, gt=0, description="最大集数")
    enable_monitoring: bool = Field(True, description="启用监控")
    github_integration: bool = Field(False, description="GitHub集成")
    vector_cache_enabled: bool = Field(True, description="启用向量缓存")
    
class YellowAlert(BaseModel):
    """Yellow队列警报模型"""
    episode_num: int = Field(..., description="集数")
    alert_type: Literal["drift", "missing_spine", "contradiction"] = Field(..., description="警报类型")
    severity: Literal["LOW", "MEDIUM", "HIGH"] = Field(..., description="严重程度")
    description: str = Field(..., description="问题描述")
    suggestions: List[str] = Field(default_factory=list, description="修改建议")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    resolved: bool = Field(False, description="是否已解决")
