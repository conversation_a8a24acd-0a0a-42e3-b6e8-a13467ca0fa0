"""
Spine事件抽取器
"""

import re
import logging
from typing import List, Dict, Any, Optional
from .models import SpineEvent
from ..io.load_save import load_json
from ..utils.text_processor import standardize_character_names

logger = logging.getLogger(__name__)

class SpineExtractor:
    """Spine事件抽取器"""
    
    def __init__(self, llm_client, alias_map_path: str = "config/alias_map.json"):
        self.llm_client = llm_client
        self.alias_map = load_json(alias_map_path)
        
    def extract_spine_events(self, chapter_summary: Dict[str, Any], max_events: int = 4) -> List[SpineEvent]:
        """从章节摘要中抽取Spine事件"""
        try:
            # 标准化角色名称
            normalized_summary = self._normalize_chapter_summary(chapter_summary)
            
            # 计算基础分数
            tension_score = self.calculate_tension_score(normalized_summary)
            
            # 使用LLM抽取事件
            spine_events = self._extract_with_llm(normalized_summary, max_events)
            
            # 规则验证和筛选
            filtered_events = self._filter_and_rank_events(spine_events, tension_score, max_events)
            
            chapter_num = chapter_summary.get('basic_info', {}).get('chapter_number', 'unknown')
            logger.info(f"Chapter {chapter_num}: extracted {len(filtered_events)} spine events")
            
            return filtered_events
            
        except Exception as e:
            logger.error(f"Failed to extract spine events: {e}")
            return self._fallback_extraction(chapter_summary)
    
    def calculate_tension_score(self, chapter_summary: Dict[str, Any]) -> float:
        """计算章节张力分数"""
        content = chapter_summary.get('narrative', {}).get('content', '')
        events = chapter_summary.get('key_events', [])
        
        score = 0.0
        
        # 冲突关键词 (权重 *2)
        conflict_keywords = ['冲突', '战斗', '争执', '对抗', '危险', '威胁', '死亡', '背叛', '决战', '生死']
        for keyword in conflict_keywords:
            score += content.count(keyword) * 0.2
        
        # 标点符号强度
        score += content.count('！') * 0.1
        score += content.count('？') * 0.05
        score += content.count('……') * 0.03
        
        # 事件数量和类型
        score += len(events) * 0.1
        
        # 角色数量 (/10)
        characters = chapter_summary.get('key_characters', [])
        score += len(characters) * 0.05
        
        # 标准化到0-1
        return min(score, 1.0)
    
    def calculate_importance_score(self, event_text: str, chapter_context: Dict[str, Any]) -> float:
        """计算事件重要性分数"""
        score = 0.5  # 基础分数
        
        # 角色发展相关
        character_keywords = ['决定', '选择', '改变', '成长', '觉悟', '转变', '领悟']
        if any(keyword in event_text for keyword in character_keywords):
            score += 0.2
            
        # 情节转折
        plot_keywords = ['发现', '揭露', '秘密', '真相', '转折', '突破', '失败', '成功']
        if any(keyword in event_text for keyword in plot_keywords):
            score += 0.3
            
        # 世界观变化
        world_keywords = ['新技术', '政策', '制度', '变革', '革命', '发明', '创新']
        if any(keyword in event_text for keyword in world_keywords):
            score += 0.2
            
        return min(score, 1.0)
    
    def _normalize_chapter_summary(self, chapter_summary: Dict[str, Any]) -> Dict[str, Any]:
        """标准化章节摘要中的角色名称"""
        normalized = chapter_summary.copy()
        
        # 标准化内容文本
        if 'narrative' in normalized and 'content' in normalized['narrative']:
            normalized['narrative']['content'] = standardize_character_names(
                normalized['narrative']['content'], self.alias_map
            )
        
        # 标准化事件描述
        if 'key_events' in normalized:
            for event in normalized['key_events']:
                if 'details' in event:
                    event['details'] = standardize_character_names(event['details'], self.alias_map)
        
        return normalized
    
    def _extract_with_llm(self, chapter_summary: Dict[str, Any], max_events: int) -> List[SpineEvent]:
        """使用LLM抽取事件"""
        from ..prompts.spine_extraction import get_spine_extraction_prompt
        
        prompt = get_spine_extraction_prompt(chapter_summary, self.alias_map, max_events)
        
        response = self.llm_client.call_json_response(
            prompt=prompt,
            expected_fields=["spine_events"]
        )
        
        spine_events = []
        for event_data in response.get('spine_events', []):
            try:
                spine_event = SpineEvent(**event_data)
                spine_events.append(spine_event)
            except (KeyError, ValueError) as e:
                logger.warning(f"Invalid spine event data: {event_data}, error: {e}")
                continue
        
        return spine_events
    
    def _filter_and_rank_events(self, events: List[SpineEvent], chapter_tension: float, max_events: int) -> List[SpineEvent]:
        """筛选和排序事件"""
        if not events:
            return []
        
        # 按重要性排序，取前2个
        events_by_importance = sorted(events, key=lambda x: x.importance, reverse=True)
        selected_events = events_by_importance[:2]
        
        # 如果章节张力高，再添加高张力事件
        if chapter_tension > 0.7:
            high_tension_events = [e for e in events if e.tension > 0.7 and e not in selected_events]
            high_tension_events.sort(key=lambda x: x.tension, reverse=True)
            selected_events.extend(high_tension_events[:2])
        
        # 限制总数
        return selected_events[:max_events]
    
    def _fallback_extraction(self, chapter_summary: Dict[str, Any]) -> List[SpineEvent]:
        """兜底抽取方法"""
        events = []
        chapter_num = chapter_summary.get('basic_info', {}).get('chapter_number', 0)
        
        # 从key_events中提取
        key_events = chapter_summary.get('key_events', [])
        for i, event in enumerate(key_events[:2]):
            spine_event = SpineEvent(
                id=f"{chapter_num}-{i+1}",
                type="plot",
                text=event.get('event', '未知事件')[:50],
                importance=0.7,
                tension=0.5
            )
            events.append(spine_event)
        
        return events
