"""
动态Phase分配器
"""

import logging
from typing import Dict, List, Any, Tuple
from collections import defaultdict
from .models import PhaseAllocation

logger = logging.getLogger(__name__)

class PhaseAllocator:
    """动态故事阶段分配器"""
    
    def __init__(self):
        self.target_ratios = {
            "setup": 0.1,
            "development": 0.4, 
            "climax": 0.3,
            "resolution": 0.2
        }
        self.tolerance = 0.1  # ±10%容忍度
    
    def calculate_phase_weights(self, group_summaries: Dict[str, Any]) -> Dict[str, float]:
        """计算每组的权重"""
        weights = {}
        
        for group_id, group_data in group_summaries.items():
            # 计算张力分数（基于组内章节）
            tension_score = self._calculate_group_tension(group_data)
            
            # 计算token比例
            token_count = group_data.get('token_count', 1000)
            token_ratio = token_count / 1000
            
            # 综合权重: 0.7*tension + 0.3*token_ratio
            weight = 0.7 * tension_score + 0.3 * token_ratio
            weights[group_id] = weight
            
        logger.info(f"Calculated weights for {len(weights)} groups")
        return weights
    
    def allocate_phases_dynamic(self, weights: Dict[str, float], group_summaries: Dict[str, Any]) -> Dict[str, Any]:
        """动态分配阶段"""
        # 第一步：轻量版分配
        initial_allocation = self._allocate_phases_simple(weights)
        
        # 生成调试报告
        debug_report = self.generate_phase_debug_report(initial_allocation, weights)
        
        # 第二步：检查是否需要自适应调整
        needs_adjustment = self._check_allocation_balance(initial_allocation, weights)
        
        if needs_adjustment:
            logger.info("Phase allocation needs adjustment, applying adaptive algorithm")
            final_allocation = self._allocate_phases_adaptive(weights, initial_allocation)
        else:
            final_allocation = initial_allocation
        
        # 验证最终分配
        validation_issues = self.validate_phase_allocation(final_allocation, group_summaries)
        
        return {
            "allocation": PhaseAllocation(**final_allocation),
            "debug_report": debug_report,
            "validation_issues": validation_issues,
            "weights": weights
        }
    
    def _calculate_group_tension(self, group_data: Dict[str, Any]) -> float:
        """计算组的张力分数"""
        # 从组摘要中提取张力指标
        summary = group_data.get('group_summary', '')
        main_events = group_data.get('main_events', [])
        
        tension = 0.0
        
        # 基于关键词
        high_tension_keywords = ['冲突', '危机', '战斗', '决战', '生死', '背叛', '揭露']
        medium_tension_keywords = ['紧张', '担忧', '困难', '挑战', '压力']
        
        for keyword in high_tension_keywords:
            tension += summary.count(keyword) * 0.3
        for keyword in medium_tension_keywords:
            tension += summary.count(keyword) * 0.1
        
        # 基于事件数量和类型
        tension += len(main_events) * 0.1
        
        # 标准化
        return min(tension, 1.0)
    
    def _allocate_phases_simple(self, weights: Dict[str, float]) -> Dict[str, List[str]]:
        """简单分桶分配"""
        # 按权重排序
        sorted_groups = sorted(weights.items(), key=lambda x: x[1])
        group_ids = [group_id for group_id, _ in sorted_groups]
        
        total_groups = len(group_ids)
        allocation = {
            "setup": [],
            "development": [],
            "climax": [],
            "resolution": []
        }
        
        # 按比例分配
        setup_count = max(1, int(total_groups * self.target_ratios["setup"]))
        dev_count = int(total_groups * self.target_ratios["development"])
        climax_count = int(total_groups * self.target_ratios["climax"])
        
        allocation["setup"] = group_ids[:setup_count]
        allocation["development"] = group_ids[setup_count:setup_count + dev_count]
        allocation["climax"] = group_ids[setup_count + dev_count:setup_count + dev_count + climax_count]
        allocation["resolution"] = group_ids[setup_count + dev_count + climax_count:]
        
        return allocation
    
    def _check_allocation_balance(self, allocation: Dict[str, List[str]], weights: Dict[str, float]) -> bool:
        """检查分配是否需要调整"""
        phase_weights = {}
        total_weight = sum(weights.values())
        
        for phase, groups in allocation.items():
            phase_weight = sum(weights[group_id] for group_id in groups)
            phase_ratio = phase_weight / total_weight if total_weight > 0 else 0
            phase_weights[phase] = phase_ratio
            
            target_ratio = self.target_ratios[phase]
            deviation = abs(phase_ratio - target_ratio) / target_ratio if target_ratio > 0 else 0
            
            if deviation > self.tolerance:
                logger.info(f"Phase {phase} deviation: {deviation:.2%} (ratio: {phase_ratio:.2%}, target: {target_ratio:.2%})")
                return True
        
        return False
    
    def _allocate_phases_adaptive(self, weights: Dict[str, float], initial_allocation: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """自适应分配算法"""
        allocation = {k: v.copy() for k, v in initial_allocation.items()}
        total_weight = sum(weights.values())
        
        # 最多调整2轮，避免震荡
        for round_num in range(2):
            adjustments_made = False
            
            for phase in ["development", "climax"]:  # 重点调整这两个阶段
                groups = allocation[phase]
                if not groups:
                    continue
                    
                phase_weight = sum(weights[group_id] for group_id in groups)
                phase_ratio = phase_weight / total_weight
                target_ratio = self.target_ratios[phase]
                
                if phase_ratio > target_ratio * 1.2:  # 需要拆分
                    # 找到权重最大的组进行拆分
                    heaviest_group = max(groups, key=lambda x: weights[x])
                    allocation[phase].remove(heaviest_group)
                    
                    # 移动到相邻阶段
                    if phase == "development":
                        allocation["setup"].append(heaviest_group)
                    else:  # climax
                        allocation["resolution"].append(heaviest_group)
                    
                    adjustments_made = True
                    logger.info(f"Moved group {heaviest_group} from {phase}")
                
                elif phase_ratio < target_ratio * 0.8:  # 需要合并
                    # 从相邻阶段借用组
                    if phase == "development" and allocation["climax"]:
                        lightest_group = min(allocation["climax"], key=lambda x: weights[x])
                        allocation["climax"].remove(lightest_group)
                        allocation["development"].append(lightest_group)
                        adjustments_made = True
                        logger.info(f"Moved group {lightest_group} to {phase}")
            
            if not adjustments_made:
                break
        
        return allocation
    
    def validate_phase_allocation(self, allocation: Dict[str, List[str]], group_summaries: Dict[str, Any]) -> List[str]:
        """验证阶段分配的合理性"""
        issues = []
        
        # 检查1: setup阶段不能为空
        if not allocation.get("setup", []):
            issues.append("Setup阶段为空")
        
        # 检查2: 验证前3个组是否在setup阶段
        all_groups = sorted(group_summaries.keys())
        first_three_groups = all_groups[:3] if len(all_groups) >= 3 else all_groups
        setup_groups = set(allocation.get("setup", []))
        
        for group_id in first_three_groups:
            if group_id not in setup_groups:
                issues.append(f"前期组 {group_id} 未分配到setup阶段")
        
        # 检查3: development阶段占比是否过高
        total_groups = sum(len(groups) for groups in allocation.values())
        if total_groups > 0:
            dev_ratio = len(allocation.get("development", [])) / total_groups
            if dev_ratio > 0.6:
                issues.append(f"Development阶段占比过高: {dev_ratio:.1%}")
        
        return issues
    
    def generate_phase_debug_report(self, allocation: Dict[str, List[str]], weights: Dict[str, float]) -> Dict[str, Any]:
        """生成阶段分配调试报告"""
        total_weight = sum(weights.values())
        report = {
            "phase_statistics": {},
            "weight_distribution": {},
            "deviations": {}
        }
        
        for phase, groups in allocation.items():
            phase_weight = sum(weights[group_id] for group_id in groups)
            phase_ratio = phase_weight / total_weight if total_weight > 0 else 0
            target_ratio = self.target_ratios[phase]
            deviation = abs(phase_ratio - target_ratio) / target_ratio if target_ratio > 0 else 0
            
            report["phase_statistics"][phase] = {
                "group_count": len(groups),
                "groups": groups,
                "total_weight": phase_weight,
                "ratio": phase_ratio,
                "target_ratio": target_ratio
            }
            
            report["deviations"][phase] = {
                "deviation": deviation,
                "within_tolerance": deviation <= self.tolerance
            }
        
        report["weight_distribution"] = weights
        
        return report
