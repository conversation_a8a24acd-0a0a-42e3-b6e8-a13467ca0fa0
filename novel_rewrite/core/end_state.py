"""
跨集状态管理器
"""

import logging
from typing import Dict, List, Any, Optional
from .models import EndState, CharacterState
from ..io.load_save import save_json, load_json

logger = logging.getLogger(__name__)

class EndStateManager:
    """跨集状态管理器"""
    
    def __init__(self, output_dir: str = "./output"):
        self.output_dir = output_dir
        self.current_state = None
        
    def generate_end_state(self, episode_script: str, episode_num: int, previous_state: Optional[EndState] = None) -> EndState:
        """从剧集脚本生成结束状态"""
        try:
            # 分析角色状态
            char_states = self._analyze_character_states(episode_script, previous_state)
            
            # 提取未解决的悬念
            open_hooks = self._extract_open_hooks(episode_script, previous_state)
            
            # 识别世界变化
            world_delta = self._identify_world_changes(episode_script, previous_state)
            
            # 创建结束状态
            end_state = EndState(
                char_states=char_states,
                open_hooks=open_hooks,
                world_delta=world_delta
            )
            
            # 保存状态
            self._save_end_state(end_state, episode_num)
            self.current_state = end_state
            
            logger.info(f"Generated end state for episode {episode_num}")
            return end_state
            
        except Exception as e:
            logger.error(f"Failed to generate end state for episode {episode_num}: {e}")
            return self._create_fallback_state(previous_state)
    
    def _analyze_character_states(self, script: str, previous_state: Optional[EndState] = None) -> Dict[str, CharacterState]:
        """分析角色状态变化"""
        char_states = {}
        
        # 从脚本中提取角色
        characters = self._extract_characters_from_script(script)
        
        for character in characters:
            # 分析角色的目标、风险、情感
            goal = self._analyze_character_goal(character, script)
            risk = self._analyze_character_risk(character, script)
            emotion = self._analyze_character_emotion(character, script)
            
            char_states[character] = CharacterState(
                goal=goal[:10],  # 限制长度
                risk=risk[:10],
                emotion=emotion[:10]
            )
        
        # 如果有前一状态，继承未变化的角色
        if previous_state:
            for char, state in previous_state.char_states.items():
                if char not in char_states:
                    char_states[char] = state
        
        return char_states
    
    def _extract_characters_from_script(self, script: str) -> List[str]:
        """从脚本中提取角色名称"""
        import re
        
        # 简单的角色名提取（基于对话格式）
        character_pattern = r'^([一-龯]{2,4})[:：]'
        characters = set()
        
        for line in script.split('\n'):
            match = re.match(character_pattern, line.strip())
            if match:
                characters.add(match.group(1))
        
        # 也从叙述中提取
        narrative_pattern = r'([一-龯]{2,4})(说|道|想|看|听|感到|决定)'
        narrative_chars = re.findall(narrative_pattern, script)
        for char, _ in narrative_chars:
            characters.add(char)
        
        return list(characters)[:5]  # 最多5个主要角色
    
    def _analyze_character_goal(self, character: str, script: str) -> str:
        """分析角色目标"""
        # 查找与角色相关的目标关键词
        goal_keywords = {
            '寻找': '寻找',
            '保护': '保护',
            '复仇': '复仇',
            '逃脱': '逃脱',
            '证明': '证明',
            '拯救': '拯救',
            '统治': '统治',
            '学习': '学习'
        }
        
        for keyword, goal in goal_keywords.items():
            if character in script and keyword in script:
                # 检查角色和关键词的距离
                char_positions = [m.start() for m in re.finditer(character, script)]
                keyword_positions = [m.start() for m in re.finditer(keyword, script)]
                
                for char_pos in char_positions:
                    for keyword_pos in keyword_positions:
                        if abs(char_pos - keyword_pos) < 100:  # 距离较近
                            return goal
        
        return "未明确"
    
    def _analyze_character_risk(self, character: str, script: str) -> str:
        """分析角色风险"""
        risk_keywords = {
            '危险': '生命危险',
            '威胁': '受到威胁',
            '追杀': '被追杀',
            '背叛': '被背叛',
            '失败': '任务失败',
            '暴露': '身份暴露',
            '陷阱': '陷入陷阱'
        }
        
        for keyword, risk in risk_keywords.items():
            if character in script and keyword in script:
                return risk
        
        return "相对安全"
    
    def _analyze_character_emotion(self, character: str, script: str) -> str:
        """分析角色情感"""
        emotion_keywords = {
            '愤怒': '愤怒',
            '悲伤': '悲伤',
            '恐惧': '恐惧',
            '喜悦': '喜悦',
            '担忧': '担忧',
            '坚定': '坚定',
            '困惑': '困惑',
            '绝望': '绝望'
        }
        
        for keyword, emotion in emotion_keywords.items():
            if character in script and keyword in script:
                return emotion
        
        return "平静"
    
    def _extract_open_hooks(self, script: str, previous_state: Optional[EndState] = None) -> List[str]:
        """提取未解决的悬念"""
        hooks = []
        
        # 悬念关键词
        hook_patterns = [
            r'([一-龯\s]{5,20})(的真相|的秘密|的身份)',
            r'(谁是|什么是|为什么)([一-龯\s]{5,20})',
            r'([一-龯\s]{5,20})(会如何|将会|能否)'
        ]
        
        for pattern in hook_patterns:
            matches = re.findall(pattern, script)
            for match in matches:
                hook = ''.join(match).strip()
                if 5 <= len(hook) <= 20:
                    hooks.append(hook)
        
        # 继承前一状态的未解决悬念
        if previous_state:
            for hook in previous_state.open_hooks:
                # 检查是否在本集中解决
                if not any(keyword in script for keyword in hook.split()):
                    hooks.append(hook)
        
        # 去重并限制数量
        hooks = list(set(hooks))[:5]
        
        return hooks
    
    def _identify_world_changes(self, script: str, previous_state: Optional[EndState] = None) -> List[str]:
        """识别世界变化"""
        changes = []
        
        # 世界变化关键词
        change_keywords = {
            '新法律': '法律变更',
            '新技术': '技术进步',
            '新发现': '重大发现',
            '政变': '政治变化',
            '战争': '战争爆发',
            '和平': '和平协议',
            '联盟': '势力联盟'
        }
        
        for keyword, change in change_keywords.items():
            if keyword in script:
                changes.append(change)
        
        # 限制数量
        return changes[:3]
    
    def _save_end_state(self, end_state: EndState, episode_num: int) -> None:
        """保存结束状态到文件"""
        import os
        
        os.makedirs(self.output_dir, exist_ok=True)
        filename = f"{self.output_dir}/end_state_ep{episode_num:02d}.json"
        
        save_json(end_state.dict(), filename)
        logger.info(f"Saved end state to {filename}")
    
    def load_end_state(self, episode_num: int) -> Optional[EndState]:
        """加载指定集数的结束状态"""
        filename = f"{self.output_dir}/end_state_ep{episode_num:02d}.json"
        
        try:
            data = load_json(filename)
            return EndState(**data)
        except FileNotFoundError:
            logger.warning(f"End state file not found: {filename}")
            return None
        except Exception as e:
            logger.error(f"Failed to load end state from {filename}: {e}")
            return None
    
    def _create_fallback_state(self, previous_state: Optional[EndState] = None) -> EndState:
        """创建兜底状态"""
        if previous_state:
            return previous_state
        
        return EndState(
            char_states={},
            open_hooks=[],
            world_delta=[]
        )
    
    def validate_state_continuity(self, current_state: EndState, previous_state: EndState) -> List[str]:
        """验证状态连续性"""
        issues = []
        
        # 检查角色状态的合理变化
        for char, prev_state in previous_state.char_states.items():
            if char in current_state.char_states:
                curr_state = current_state.char_states[char]
                
                # 检查是否有不合理的状态跳跃
                if prev_state.emotion == "绝望" and curr_state.emotion == "喜悦":
                    issues.append(f"{char}的情感变化过于突然")
        
        # 检查悬念的处理
        for hook in previous_state.open_hooks:
            if hook not in current_state.open_hooks:
                # 悬念被解决了，这是正常的
                pass
        
        return issues
