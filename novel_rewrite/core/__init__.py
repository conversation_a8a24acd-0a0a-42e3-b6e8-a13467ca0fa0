"""
核心算法模块

包含小说改编系统的核心算法：
- Spine事件抽取
- 角色别名管理  
- 动态Phase分配
- 剧情漂移检测
- 跨集状态管理
"""

from .models import SpineEvent, EndState, QualityReport, EpisodeMetrics
from .spine import SpineExtractor
from .alias import AliasManager
from .phase import PhaseAllocator
from .drift import DriftDetector
from .end_state import EndStateManager

__all__ = [
    "SpineEvent",
    "EndState", 
    "QualityReport",
    "EpisodeMetrics",
    "SpineExtractor",
    "AliasManager",
    "PhaseAllocator", 
    "DriftDetector",
    "EndStateManager"
]

__version__ = "2.0.0"
