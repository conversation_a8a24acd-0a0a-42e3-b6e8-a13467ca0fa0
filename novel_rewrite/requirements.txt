# novel_rewrite/requirements.txt
# Core dependencies
pydantic>=2.0.0
numpy>=1.24.0
pandas>=2.0.0
sqlite3
faiss-cpu>=1.7.0
sentence-transformers>=2.2.0
transformers>=4.30.0
torch>=2.0.0

# Text processing
jieba>=0.42.1
opencc-python-reimplemented>=0.1.7
regex>=2023.6.3

# API and web
requests>=2.31.0
httpx>=0.24.0
fastapi>=0.100.0
uvicorn>=0.22.0

# Utilities
tqdm>=4.65.0
click>=8.1.0
python-dotenv>=1.0.0
pyyaml>=6.0
loguru>=0.7.0

# Development
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
mypy>=1.4.0

# GitHub integration
PyGithub>=1.59.0
