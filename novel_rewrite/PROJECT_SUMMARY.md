# 小说改编剧本生成系统 v2.0 - 项目总结

## 🎯 项目概述

这是一个基于Key-Event Spine和动态Phase分配的智能小说改编系统，旨在解决传统改编中的剧情漂移和质量控制问题。系统采用轻量级事件锚点平衡忠实度与创作自由，通过实时监控确保生成质量。

## 📁 完整项目结构

```
novel_rewrite/
├── README.md                          # 项目说明文档
├── requirements.txt                   # Python依赖包
├── bootstrap_spec.yaml                # 项目规范配置
├── example_usage.py                   # 使用示例脚本
├── PROJECT_SUMMARY.md                 # 项目总结文档
│
├── core/                              # 核心算法模块
│   ├── __init__.py                    # 模块初始化
│   ├── models.py                      # Pydantic数据模型
│   ├── spine.py                       # Spine事件抽取器
│   ├── alias.py                       # 角色别名管理器
│   ├── phase.py                       # 动态Phase分配器
│   ├── drift.py                       # 剧情漂移检测器
│   └── end_state.py                   # 跨集状态管理器
│
├── io/                                # 输入输出处理
│   ├── __init__.py                    # 模块初始化
│   ├── load_save.py                   # 统一I/O操作
│   ├── vector_cache.py                # 向量缓存系统
│   └── github_bot.py                  # GitHub集成机器人
│
├── prompts/                           # LLM提示词模板
│   ├── __init__.py                    # 模块初始化
│   ├── spine_extraction.md            # Spine抽取说明文档
│   ├── spine_extraction.py            # Spine抽取提示词
│   ├── episode_structure.md           # 剧集结构说明文档
│   ├── episode_structure.py           # 剧集结构提示词
│   ├── script_generation.py           # 剧本生成提示词
│   └── quality_review.py              # 质量评审提示词
│
├── pipelines/                         # 处理流水线
│   ├── __init__.py                    # 模块初始化
│   ├── build_spine.py                 # Spine构建流水线
│   ├── build_phases.py                # Phase分配流水线
│   ├── build_episodes.py              # 剧集生成流水线
│   └── full_pipeline.py               # 完整处理流水线
│
├── config/                            # 配置管理
│   ├── settings.py                    # 主配置文件
│   ├── alias_map.json                 # 角色别名映射
│   └── model_config.yaml              # 模型参数配置
│
├── utils/                             # 工具函数
│   ├── __init__.py                    # 模块初始化
│   ├── text_processor.py              # 文本处理工具
│   └── quality_monitor.py             # 质量监控工具
│
└── tests/                             # 测试代码
    ├── __init__.py                    # 测试初始化
    ├── test_core.py                   # 核心功能测试
    └── fixtures/                      # 测试数据
```

## 🔧 核心技术特性

### 1. Key-Event Spine 系统
- **轻量级锚点**: 每章抽取1-4个关键事件，平衡忠实度与创作自由
- **双维度评分**: importance(重要性) + tension(张力) 分离评估
- **智能筛选**: 按重要性优先 + 高张力事件补充的筛选策略
- **别名标准化**: 自动处理角色称呼变化，确保一致性

### 2. 动态Phase分配算法
- **权重计算**: 0.7×tension + 0.3×token_ratio 的综合权重
- **自适应调整**: 偏差>20%自动拆分，<80%自动合并
- **容忍机制**: ±10%浮动空间，避免过度调整
- **验证规则**: 前3组必须在setup，development不超过60%

### 3. 剧情漂移检测
- **多模型融合**: xiaobu-sbert + mxbai-large 向量平均
- **动态阈值**: median + IQR 抗极端值干扰
- **三层缓存**: 内存LRU + SQLite + 分片存储
- **实时监控**: 连续3集Yellow自动暂停流水线

### 4. Yellow队列管理
- **GitHub集成**: 自动创建Issue，支持Apply Suggestion
- **严重程度分级**: HIGH/MEDIUM/LOW 三级分类
- **批量处理**: 支持批量创建和状态同步
- **可视化报告**: Markdown格式，便于人工审核

## 📊 质量控制指标

| 指标 | 目标值 | 当前实现 |
|------|--------|----------|
| 剧情漂移率 | <15% | 通过Spine锚点控制 |
| 人工改动率 | <20% | Yellow队列管理 |
| 时长误差 | ±15% | TTS速度估算 |
| 处理速度 | <10min/1k章 | 向量缓存优化 |

## 🚀 使用流程

### 1. 环境配置
```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
export OPENAI_API_KEY="your-api-key"
export GITHUB_TOKEN="your-github-token"
export VECTOR_CACHE_DIR="./cache"
```

### 2. 数据准备
```json
{
  "chapters": [
    {
      "basic_info": {"chapter_number": 1, "title": "开始"},
      "narrative": {"content": "章节内容..."},
      "key_events": [{"event": "关键事件", "importance": 0.8}],
      "key_characters": ["角色1", "角色2"]
    }
  ]
}
```

### 3. 运行流水线
```bash
# 完整流水线
python pipelines/full_pipeline.py input.json --compression 0.3

# 分步执行
python pipelines/build_spine.py input.json
python pipelines/build_phases.py spine_events.json  
python pipelines/build_episodes.py phase_allocation.json
```

### 4. 质量监控
```bash
# 查看Yellow队列
python utils/quality_monitor.py --show_yellow_queue

# 生成质量报告
python utils/quality_monitor.py --generate_report output/
```

## 🔍 监控和调试

### 输出文件结构
```
output/
├── spine_events.json              # Spine事件抽取结果
├── spine_validation.json          # Spine质量验证报告
├── phase_allocation.json          # Phase分配结果
├── phase_allocation_debug.json    # Phase分配调试信息
├── episodes.json                  # 剧集生成结果
├── quality_metrics.json           # 质量指标统计
├── scripts/                       # 单集剧本文件
│   ├── episode_01.txt
│   └── episode_02.txt
├── quality_reports/               # 质量报告
│   ├── episode_01.json
│   └── episode_02.json
└── yellow_queue/                  # Yellow队列
    ├── episode_03.md
    └── episode_05.md
```

### 关键监控指标
- **divergence_score**: 剧情漂移分数 (0-1)
- **spine_coverage**: Spine事件覆盖率
- **yellow_queue_size**: 待处理问题数量
- **processing_time**: 处理耗时统计

## 🧪 测试和验证

### 单元测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_core.py::TestSpineExtractor
```

### 集成测试
```bash
# 运行示例
python example_usage.py

# 验证输出
ls output/
cat output/spine_summary.md
```

## 🔧 配置和定制

### 主要配置项
- **compression_ratio**: 压缩比例 (0.3-0.7)
- **max_episodes**: 最大集数限制
- **quality_thresholds**: 质量控制阈值
- **vector_models**: 向量编码模型列表

### 扩展点
- **自定义LLM客户端**: 实现call_json_response接口
- **自定义质量检测**: 继承DriftDetector类
- **自定义提示词**: 修改prompts/目录下的模板
- **自定义监控**: 扩展QualityMonitor类

## 📈 性能优化

### 已实现优化
- **向量缓存**: 三层缓存架构，减少重复计算
- **批量处理**: 支持批量编码和处理
- **异步写入**: 非阻塞的缓存写入
- **内存管理**: LRU缓存 + 定期清理

### 进一步优化建议
- **并行处理**: 多进程处理不同章节
- **模型量化**: 使用量化模型减少内存占用
- **增量更新**: 支持增量章节处理
- **分布式部署**: 支持多机分布式处理

## 🤝 贡献指南

### 开发环境设置
```bash
# 克隆项目
git clone <repository-url>
cd novel_rewrite

# 安装开发依赖
pip install -r requirements.txt
pip install pytest black isort mypy

# 运行代码格式化
black .
isort .

# 运行类型检查
mypy .
```

### 提交规范
- 功能开发: `feat: 添加新功能`
- 问题修复: `fix: 修复bug`
- 文档更新: `docs: 更新文档`
- 测试添加: `test: 添加测试`

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

- 项目维护者: [维护者信息]
- 问题反馈: [GitHub Issues]
- 技术讨论: [讨论区链接]

---

*本项目基于Claude Sonnet 4模型开发，致力于提供高质量的小说改编解决方案。*
