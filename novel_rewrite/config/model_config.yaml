# 模型配置文件

# LLM模型配置
llm_models:
  primary:
    provider: "openai"
    model: "gpt-4"
    temperature: 0.7
    max_tokens: 4000
    timeout: 60
  
  fallback:
    provider: "openai" 
    model: "gpt-3.5-turbo"
    temperature: 0.7
    max_tokens: 3000
    timeout: 30

# 向量模型配置
embedding_models:
  primary:
    name: "xiaobu-embedding/gte-Qwen2-1.5B-instruct"
    dimension: 1536
    max_length: 512
    batch_size: 32
  
  secondary:
    name: "mxbai-embed-large"
    dimension: 1024
    max_length: 512
    batch_size: 16

# 质量控制参数
quality_control:
  divergence_thresholds:
    setup_phase: 0.45      # setup阶段更严格
    development_phase: 0.50 # 标准阈值
    climax_phase: 0.55     # climax阶段更宽松
    resolution_phase: 0.50
  
  yellow_queue:
    max_retry_attempts: 3
    auto_pause_threshold: 0.15  # 15%的集数为yellow时暂停
    severity_rules:
      high: 
        - "divergence_score > 0.7"
        - "missing_importance > 0.8"
      medium:
        - "divergence_score > 0.5"
        - "missing_spine_events > 0"
      low:
        - "divergence_score > 0.3"

# 处理流程配置
processing:
  spine_extraction:
    max_events_per_chapter: 4
    min_importance_threshold: 0.3
    min_tension_threshold: 0.2
  
  phase_allocation:
    target_ratios:
      setup: 0.1
      development: 0.4
      climax: 0.3
      resolution: 0.2
    tolerance: 0.1
    max_adjustment_rounds: 2
  
  episode_generation:
    target_duration_minutes: 12.0
    max_scenes_per_episode: 6
    min_scenes_per_episode: 4
    scene_duration_range: [2.0, 3.5]

# 缓存配置
cache:
  vector_cache:
    memory_limit: 1000
    cleanup_interval_days: 7
    max_age_days: 30
    compression_enabled: true
  
  file_cache:
    max_size_mb: 512
    cleanup_threshold: 0.8

# 监控配置
monitoring:
  metrics:
    - "divergence_score"
    - "spine_coverage"
    - "processing_time"
    - "yellow_queue_size"
    - "memory_usage"
  
  alerts:
    email_enabled: false
    webhook_enabled: false
    github_issues_enabled: true
  
  dashboard:
    refresh_interval: 30
    history_days: 30

# GitHub集成配置
github:
  issue_labels:
    quality_issue: "quality-issue"
    auto_generated: "auto-generated"
    severity_high: "severity-high"
    severity_medium: "severity-medium"
    severity_low: "severity-low"
  
  templates:
    issue_title: "Episode {episode_num} Quality Issues - {severity}"
    comment_prefix: "Auto-generated quality report"
