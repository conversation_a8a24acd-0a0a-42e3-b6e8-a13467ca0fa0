"""
配置管理模块
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path

class Settings:
    """系统配置管理"""
    
    def __init__(self):
        # 基础路径
        self.PROJECT_ROOT = Path(__file__).parent.parent
        self.CONFIG_DIR = self.PROJECT_ROOT / "config"
        self.CACHE_DIR = Path(os.getenv("VECTOR_CACHE_DIR", "./cache"))
        self.OUTPUT_DIR = Path(os.getenv("OUTPUT_DIR", "./output"))
        
        # API配置
        self.OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
        self.OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        self.OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4")
        
        # GitHub集成
        self.GITHUB_TOKEN = os.getenv("GITHUB_TOKEN")
        self.GITHUB_REPO = os.getenv("GITHUB_REPO")
        
        # 向量模型配置
        self.VECTOR_MODELS = [
            "xiaobu-embedding/gte-Qwen2-1.5B-instruct",
            "mxbai-embed-large"
        ]
        
        # 质量控制参数
        self.QUALITY_THRESHOLDS = {
            "divergence_base": 0.45,
            "divergence_max": 0.55,
            "yellow_threshold": 0.5,
            "red_threshold": 0.7
        }
        
        # 处理参数
        self.PROCESSING_CONFIG = {
            "compression_ratio": 0.3,
            "max_episodes": None,
            "target_duration": 12.0,
            "max_scenes_per_episode": 6,
            "max_spine_events": 4
        }
        
        # 缓存配置
        self.CACHE_CONFIG = {
            "memory_limit": 1000,
            "cleanup_days": 30,
            "max_cache_size_mb": 1024
        }
        
        # 日志配置
        self.LOG_CONFIG = {
            "level": os.getenv("LOG_LEVEL", "INFO"),
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file": "novel_rewrite.log"
        }
        
        # 确保目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.CACHE_DIR, self.OUTPUT_DIR]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_alias_map_path(self) -> str:
        """获取别名映射文件路径"""
        return str(self.CONFIG_DIR / "alias_map.json")
    
    def get_model_config_path(self) -> str:
        """获取模型配置文件路径"""
        return str(self.CONFIG_DIR / "model_config.yaml")
    
    def update_from_dict(self, config_dict: Dict[str, Any]) -> None:
        """从字典更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            key: value for key, value in self.__dict__.items()
            if not key.startswith('_')
        }
    
    def validate(self) -> list[str]:
        """验证配置有效性"""
        issues = []
        
        if not self.OPENAI_API_KEY:
            issues.append("OPENAI_API_KEY not set")
        
        if self.GITHUB_TOKEN and not self.GITHUB_REPO:
            issues.append("GITHUB_TOKEN set but GITHUB_REPO not specified")
        
        if not self.CACHE_DIR.exists():
            issues.append(f"Cache directory does not exist: {self.CACHE_DIR}")
        
        return issues

# 全局配置实例
settings = Settings()

# 配置验证
def validate_settings() -> None:
    """验证配置并输出警告"""
    issues = settings.validate()
    if issues:
        import logging
        logger = logging.getLogger(__name__)
        for issue in issues:
            logger.warning(f"Configuration issue: {issue}")

# 环境特定配置
def load_environment_config(env: str = "development") -> None:
    """加载环境特定配置"""
    env_file = settings.CONFIG_DIR / f"{env}.env"
    if env_file.exists():
        from dotenv import load_dotenv
        load_dotenv(env_file)
        
        # 重新初始化设置
        global settings
        settings = Settings()

# 配置工厂函数
def create_config(
    compression_ratio: float = 0.3,
    max_episodes: Optional[int] = None,
    enable_monitoring: bool = True,
    github_integration: bool = False
) -> Dict[str, Any]:
    """创建处理配置"""
    config = settings.PROCESSING_CONFIG.copy()
    config.update({
        "compression_ratio": compression_ratio,
        "max_episodes": max_episodes,
        "enable_monitoring": enable_monitoring,
        "github_integration": github_integration
    })
    return config
