#!/usr/bin/env python3
"""
使用示例：小说改编剧本生成系统
"""

import json
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_input():
    """创建示例输入数据"""
    sample_data = {
        "novel_info": {
            "title": "放开那个女巫",
            "author": "二目",
            "genre": "奇幻",
            "description": "现代工程师穿越到异世界，利用现代知识改变世界的故事"
        },
        "chapters": [
            {
                "basic_info": {
                    "chapter_number": 1,
                    "title": "穿越",
                    "word_count": 3500
                },
                "narrative": {
                    "content": "程岩意外穿越到异世界，成为边境镇的四王子罗兰。他发现这个世界有女巫存在，而且即将面临魔鬼的入侵。罗兰决定利用现代知识来改变这个世界的命运。"
                },
                "key_events": [
                    {"event": "程岩穿越成为罗兰", "importance": 0.9},
                    {"event": "了解女巫和魔鬼的存在", "importance": 0.8},
                    {"event": "决定改变世界", "importance": 0.7}
                ],
                "key_characters": ["罗兰", "程岩"]
            },
            {
                "basic_info": {
                    "chapter_number": 2,
                    "title": "第一个女巫",
                    "word_count": 4200
                },
                "narrative": {
                    "content": "罗兰遇到了第一个女巫安娜，她拥有控制火焰的能力。罗兰决定保护她，而不是按照传统处死女巫。这个决定震惊了所有人，包括骑士长卡特。"
                },
                "key_events": [
                    {"event": "遇到女巫安娜", "importance": 0.9},
                    {"event": "决定保护女巫", "importance": 0.95},
                    {"event": "与传统观念冲突", "importance": 0.7}
                ],
                "key_characters": ["罗兰", "安娜", "卡特"]
            },
            {
                "basic_info": {
                    "chapter_number": 3,
                    "title": "火药的力量",
                    "word_count": 3800
                },
                "narrative": {
                    "content": "罗兰开始利用现代化学知识制造火药。他向安娜展示了火药的威力，并开始训练边境镇的民兵。同时，他也在思考如何更好地利用女巫的能力。"
                },
                "key_events": [
                    {"event": "制造第一批火药", "importance": 0.8},
                    {"event": "展示火药威力", "importance": 0.7},
                    {"event": "开始军事改革", "importance": 0.75}
                ],
                "key_characters": ["罗兰", "安娜", "民兵"]
            }
        ]
    }
    
    return sample_data

def run_example():
    """运行示例"""
    logger.info("开始运行小说改编示例")
    
    # 1. 创建示例数据
    sample_data = create_sample_input()
    
    # 保存示例输入
    input_file = Path("sample_input.json")
    with open(input_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"示例输入数据已保存到: {input_file}")
    
    # 2. 模拟LLM客户端
    class MockLLMClient:
        def call_json_response(self, prompt, expected_fields=None):
            """模拟LLM响应"""
            if "spine_events" in expected_fields:
                return {
                    "spine_events": [
                        {
                            "id": "1-1",
                            "type": "plot",
                            "text": "程岩穿越成为罗兰",
                            "importance": 0.9,
                            "tension": 0.6
                        },
                        {
                            "id": "1-2", 
                            "type": "emotion",
                            "text": "决定改变世界命运",
                            "importance": 0.8,
                            "tension": 0.7
                        }
                    ]
                }
            return {}
    
    # 3. 运行Spine抽取示例
    try:
        from pipelines.build_spine import SpinePipeline
        
        llm_client = MockLLMClient()
        spine_pipeline = SpinePipeline(llm_client, "./output")
        
        logger.info("开始Spine事件抽取...")
        spine_results = spine_pipeline.process_chapters(sample_data["chapters"])
        
        logger.info(f"Spine抽取完成，共处理 {len(spine_results)} 个章节")
        
        # 验证结果
        validation_results = spine_pipeline.validate_spine_events(spine_results, sample_data["chapters"])
        logger.info(f"Spine验证完成，整体质量: {validation_results['overall_quality']}")
        
        # 导出摘要
        summary_file = spine_pipeline.export_spine_summary(spine_results)
        logger.info(f"Spine摘要已导出到: {summary_file}")
        
    except ImportError as e:
        logger.warning(f"无法导入Spine模块: {e}")
        logger.info("这是正常的，因为这只是一个示例")
    
    # 4. 显示配置信息
    try:
        from config.settings import settings
        
        logger.info("当前配置:")
        logger.info(f"  输出目录: {settings.OUTPUT_DIR}")
        logger.info(f"  缓存目录: {settings.CACHE_DIR}")
        logger.info(f"  向量模型: {settings.VECTOR_MODELS}")
        logger.info(f"  质量阈值: {settings.QUALITY_THRESHOLDS}")
        
    except ImportError as e:
        logger.warning(f"无法导入配置模块: {e}")
    
    # 5. 显示使用说明
    print("\n" + "="*50)
    print("使用说明:")
    print("="*50)
    print("1. 安装依赖: pip install -r requirements.txt")
    print("2. 配置环境变量:")
    print("   export OPENAI_API_KEY='your-api-key'")
    print("   export GITHUB_TOKEN='your-github-token' (可选)")
    print("3. 运行完整流水线:")
    print("   python pipelines/full_pipeline.py sample_input.json")
    print("4. 查看结果:")
    print("   ls output/")
    print("5. 监控质量:")
    print("   python utils/quality_monitor.py --show_yellow_queue")
    print("="*50)

if __name__ == "__main__":
    run_example()
